# Docker Compose للإنتاج - Ta9affi عالي الأداء
version: '3.8'

services:
  # قاعدة البيانات PostgreSQL
  postgres:
    image: postgres:15-alpine
    container_name: ta9affi_postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: ta9affi_production
      POSTGRES_USER: ta9affi_user
      POSTGRES_PASSWORD: ta9affi_secure_password_2024
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - ta9affi_network
    command: >
      postgres
      -c max_connections=200
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c maintenance_work_mem=64MB
      -c checkpoint_completion_target=0.9
      -c wal_buffers=16MB
      -c default_statistics_target=100
      -c random_page_cost=1.1
      -c effective_io_concurrency=200
      -c work_mem=4MB
      -c min_wal_size=1GB
      -c max_wal_size=4GB
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ta9affi_user -d ta9affi_production"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Redis للتخزين المؤقت
  redis:
    image: redis:7-alpine
    container_name: ta9affi_redis
    restart: unless-stopped
    command: >
      redis-server
      --maxmemory 512mb
      --maxmemory-policy allkeys-lru
      --save 900 1
      --save 300 10
      --save 60 10000
      --appendonly yes
      --appendfsync everysec
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - ta9affi_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5

  # تطبيق Ta9affi الرئيسي
  ta9affi:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: ta9affi_app
    restart: unless-stopped
    environment:
      # إعدادات البيئة
      FLASK_ENV: production
      FLASK_APP: app.py
      
      # قاعدة البيانات
      POSTGRES_HOST: postgres
      POSTGRES_PORT: 5432
      POSTGRES_DB: ta9affi_production
      POSTGRES_USER: ta9affi_user
      POSTGRES_PASSWORD: ta9affi_secure_password_2024
      
      # Redis
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_DB: 0
      
      # الأمان
      SECRET_KEY: ta9affi-production-secret-key-2024-very-secure
      
      # Chargily
      CHARGILY_PUBLIC_KEY: live_pk_2pD7cep2GCAuBHDxXXegTAkrOLBrnD59tkyZeGCk
      CHARGILY_SECRET_KEY: live_sk_914RIuLl0mtEjHhSvhylpDMnPiadv74Gp0DTiNpU
      CHARGILY_WEBHOOK_URL: https://ta9affi.com/chargily-webhook
      
      # البريد الإلكتروني (اختياري)
      MAIL_SERVER: smtp.gmail.com
      MAIL_PORT: 587
      MAIL_USERNAME: ${MAIL_USERNAME:-}
      MAIL_PASSWORD: ${MAIL_PASSWORD:-}
      MAIL_DEFAULT_SENDER: <EMAIL>
      
      # تهيئة قاعدة البيانات
      INIT_DB: "true"
      
    volumes:
      - app_uploads:/app/uploads
      - app_logs:/app/logs
      - app_static:/app/static
    ports:
      - "80:80"
      - "5000:5000"
    networks:
      - ta9affi_network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

# الشبكات
networks:
  ta9affi_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# التخزين المستمر
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  app_uploads:
    driver: local
  app_logs:
    driver: local
  app_static:
    driver: local
