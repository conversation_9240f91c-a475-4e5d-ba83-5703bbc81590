# حل مشكلة HTTPS في dokploy

## 1. التحقق من إعدادات Domain في dokploy

في لوحة تحكم dokploy:

### أ) إعدادات Domain الحالية:
```
Domain: ta9affi.com
Path: /
Port: 80  ← هذا خطأ!
HTTPS: ✓
Cert: letsencrypt
```

### ب) الإعدادات الصحيحة:
```
Domain: ta9affi.com
Path: /
Port: 8000  ← يجب أن يكون 8000 (منفذ التطبيق)
HTTPS: ✓
Cert: letsencrypt
```

## 2. إضافة إعدادات إضافية

### أ) إضافة www subdomain:
```
Domain: www.ta9affi.com
Path: /
Port: 8000
HTTPS: ✓
Cert: letsencrypt
```

### ب) إعداد redirect من HTTP إلى HTTPS:
في dokploy، فعّل "Force HTTPS Redirect"

## 3. التحقق من حالة الشهادة

في terminal dokploy أو SSH:

```bash
# فحص الشهادة
openssl s_client -connect ta9affi.com:443 -servername ta9affi.com

# فحص DNS
nslookup ta9affi.com

# فحص المنافذ
netstat -tlnp | grep :443
```

## 4. إعادة إنشاء الشهادة

إذا لم تعمل الحلول السابقة:

1. احذف Domain من dokploy
2. انتظر 5 دقائق
3. أضف Domain مرة أخرى بالإعدادات الصحيحة
4. انتظر حتى يتم إنشاء الشهادة

## 5. فحص logs

في dokploy:
- اذهب إلى Application → Logs
- ابحث عن أخطاء SSL/TLS
- تحقق من nginx logs

## 6. إعدادات nginx إضافية (إذا لزم الأمر)

إذا كان dokploy يستخدم nginx مخصص، تأكد من:

```nginx
server {
    listen 443 ssl http2;
    server_name ta9affi.com www.ta9affi.com;
    
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 7. التحقق النهائي

بعد التطبيق:
```bash
curl -I https://ta9affi.com/
curl -I https://www.ta9affi.com/
```

يجب أن ترى:
```
HTTP/2 200
server: nginx
```
