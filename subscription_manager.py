"""
نظام إدارة الاشتراكات
"""

from datetime import datetime, timedelta
from flask import current_app
from models_new import db, User, Subscription, SubscriptionPlan, Payment, SubscriptionNotification, SubscriptionExtensionLog, Role
from chargily_pay import ChargilyClient
from chargily_pay.settings import CHARGILIY_TEST_URL, CHARGILIY_URL
from chargily_pay.entity import Checkout, Product, Price
import json

class SubscriptionManager:
    """مدير الاشتراكات"""
    
    def __init__(self):
        # إعدادات Chargily (Live mode - بيانات فعلية)
        # ⚠️ تحذير: هذه مفاتيح Live mode الفعلية - لا تشاركها مع أحد
        self.chargily_public_key = "live_pk_2pD7cep2GCAuBHDxXXegTAkrOLBrnD59tkyZeGCk"
        self.chargily_secret_key = "live_sk_914RIuLl0mtEjHhSvhylpDMnPiadv74Gp0DTiNpU"
        self.webhook_url = "http://ta9affi.com/chargily-webhook"

        # تهيئة عميل Chargily (Live mode)
        self.chargily = ChargilyClient(
            self.chargily_public_key,
            self.chargily_secret_key,
            CHARGILIY_URL  # Live URL
        )

    def initialize_subscription_plans(self):
        """تهيئة باقات الاشتراك الافتراضية"""
        plans = [
            {
                'name': 'الباقة الشهرية',
                'description': 'اشتراك لمدة شهر واحد',
                'price': 1000.0,
                'duration_months': 1,
                'is_institutional': False
            },
            {
                'name': 'الباقة الفصلية',
                'description': 'اشتراك لمدة 3 أشهر',
                'price': 2000.0,
                'duration_months': 3,
                'is_institutional': False
            },
            {
                'name': 'الباقة السنوية',
                'description': 'اشتراك للموسم الدراسي الكامل (9 أشهر)',
                'price': 5000.0,
                'duration_months': 9,
                'is_institutional': False
            },
            {
                'name': 'الباقة المؤسسية',
                'description': 'للمؤسسات الخاصة والنقابات المعتمدة - لمعلومات أكثر اتصل بالإدارة',
                'price': 0.0,  # سعر قابل للتفاوض
                'duration_months': 12,
                'is_institutional': True
            }
        ]
        
        for plan_data in plans:
            existing_plan = SubscriptionPlan.query.filter_by(name=plan_data['name']).first()
            if not existing_plan:
                plan = SubscriptionPlan(**plan_data)
                db.session.add(plan)
        
        db.session.commit()

    def get_available_plans(self, user=None):
        """الحصول على الباقات المتاحة"""
        if user and user.role == Role.INSPECTOR:
            # المفتشون يحصلون على جميع الباقات عند تفعيل تسجيلهم
            return SubscriptionPlan.query.filter_by(is_active=True).all()
        else:
            # الأساتذة يحصلون على الباقات غير المؤسسية فقط
            return SubscriptionPlan.query.filter_by(is_active=True, is_institutional=False).all()

    def create_subscription(self, user_id, plan_id, is_free_trial=False):
        """إنشاء اشتراك جديد"""
        user = User.query.get(user_id)
        plan = SubscriptionPlan.query.get(plan_id)
        
        if not user or not plan:
            return None
            
        # حساب تواريخ الاشتراك
        start_date = datetime.utcnow()
        end_date = start_date + timedelta(days=plan.duration_months * 30)
        
        # إنشاء الاشتراك
        subscription = Subscription(
            user_id=user_id,
            plan_id=plan_id,
            start_date=start_date,
            end_date=end_date,
            is_free_trial=is_free_trial,
            is_active=True
        )
        
        db.session.add(subscription)
        
        # تحديث حالة المستخدم
        user.subscription_status = 'active'
        
        db.session.commit()
        return subscription

    def create_payment_checkout(self, user_id, plan_id, success_url, failure_url):
        """إنشاء عملية دفع عبر Chargily"""
        # كتابة السجلات في ملف منفصل
        with open('payment_debug.log', 'a', encoding='utf-8') as f:
            f.write(f"🔄 [SubscriptionManager] بدء create_payment_checkout\n")
            f.write(f"   - User ID: {user_id}\n")
            f.write(f"   - Plan ID: {plan_id}\n")
            f.write(f"   - Success URL: {success_url}\n")
            f.write(f"   - Failure URL: {failure_url}\n")

        print(f"🔄 [SubscriptionManager] بدء create_payment_checkout")
        print(f"   - User ID: {user_id}")
        print(f"   - Plan ID: {plan_id}")
        print(f"   - Success URL: {success_url}")
        print(f"   - Failure URL: {failure_url}")

        user = User.query.get(user_id)
        plan = SubscriptionPlan.query.get(plan_id)

        if not user:
            print(f"❌ [SubscriptionManager] المستخدم غير موجود: {user_id}")
            return None

        if not plan:
            print(f"❌ [SubscriptionManager] الباقة غير موجودة: {plan_id}")
            return None

        print(f"✅ [SubscriptionManager] المستخدم: {user.username}")
        print(f"✅ [SubscriptionManager] الباقة: {plan.name} - {plan.price} دج")

        try:
            # إنشاء منتج في Chargily
            print(f"🔄 [SubscriptionManager] إنشاء منتج في Chargily...")
            product = Product(
                name=f"اشتراك {plan.name} - {user.username}",
                description=plan.description
            )
            product_response = self.chargily.create_product(product)
            product_id = product_response["id"]
            print(f"✅ [SubscriptionManager] تم إنشاء المنتج: {product_id}")
            
            # إنشاء سعر في Chargily
            print(f"🔄 [SubscriptionManager] إنشاء سعر في Chargily...")
            price = Price(
                amount=int(plan.price),  # المبلغ بالدينار الجزائري
                currency="dzd",
                product_id=product_id
            )
            price_response = self.chargily.create_price(price)
            price_id = price_response["id"]
            print(f"✅ [SubscriptionManager] تم إنشاء السعر: {price_id}")
            
            # إنشاء عملية الدفع
            print(f"🔄 [SubscriptionManager] إنشاء checkout في Chargily...")
            checkout = Checkout(
                items=[{"price": price_id, "quantity": 1}],
                success_url=success_url,
                failure_url=failure_url,
                metadata={
                    "user_id": str(user_id),
                    "plan_id": str(plan_id),
                    "username": user.username
                }
            )

            checkout_response = self.chargily.create_checkout(checkout)
            print(f"✅ [SubscriptionManager] تم إنشاء checkout: {checkout_response['id']}")
            
            # حفظ معلومات الدفع في قاعدة البيانات
            print(f"🔄 [SubscriptionManager] حفظ الدفع في قاعدة البيانات...")
            payment = Payment(
                user_id=user_id,
                plan_id=plan_id,  # إضافة plan_id
                subscription_id=None,  # سيتم تحديثه بعد إنشاء الاشتراك
                amount=plan.price,
                currency='DZD',
                status='pending',
                chargily_checkout_id=checkout_response["id"],
                chargily_response=json.dumps(checkout_response)
            )

            db.session.add(payment)
            db.session.commit()
            print(f"✅ [SubscriptionManager] تم حفظ الدفع: Payment ID {payment.id}")

            result = {
                'checkout_url': checkout_response["checkout_url"],
                'checkout_id': checkout_response["id"],
                'payment_id': payment.id
            }
            print(f"✅ [SubscriptionManager] إرجاع النتيجة: {result['checkout_url']}")
            return result
            
        except Exception as e:
            with open('payment_debug.log', 'a', encoding='utf-8') as f:
                f.write(f"❌ [SubscriptionManager] خطأ في create_payment_checkout:\n")
                f.write(f"   - Error: {str(e)}\n")
                f.write(f"   - Type: {type(e)}\n")
                import traceback
                f.write(f"   - Traceback: {traceback.format_exc()}\n")

            print(f"❌ [SubscriptionManager] خطأ في create_payment_checkout:")
            print(f"   - Error: {str(e)}")
            print(f"   - Type: {type(e)}")
            import traceback
            print(f"   - Traceback: {traceback.format_exc()}")
            return None

    def process_payment_webhook(self, webhook_data):
        """معالجة webhook من Chargily (الدالة القديمة للتوافق)"""
        success, result = self.process_payment_webhook_enhanced(webhook_data)
        return success

    def process_payment_webhook_enhanced(self, webhook_data, request_id=None):
        """معالجة webhook محسنة من Chargily مع ضمان عودة البيانات"""

        if not request_id:
            request_id = f"webhook_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"

        try:
            print(f"[{request_id}] 🔄 Starting webhook processing...")

            # 1. استخراج البيانات الأساسية
            checkout_id = webhook_data.get('checkout_id')
            status = webhook_data.get('status')

            print(f"[{request_id}] 📋 Webhook data:")
            print(f"[{request_id}]    - Checkout ID: {checkout_id}")
            print(f"[{request_id}]    - Status: {status}")

            # 2. البحث عن الدفعة في قاعدة البيانات
            print(f"[{request_id}] 🔍 Searching for payment...")
            payment = Payment.query.filter_by(chargily_checkout_id=checkout_id).first()

            if not payment:
                error_msg = f"Payment not found for checkout_id: {checkout_id}"
                print(f"[{request_id}] ❌ {error_msg}")
                return False, {'error': error_msg, 'checkout_id': checkout_id}

            print(f"[{request_id}] ✅ Payment found: ID={payment.id}, User={payment.user_id}")

            # 3. تحديث حالة الدفع
            old_status = payment.status
            payment.status = status
            payment.updated_at = datetime.utcnow()

            # حفظ بيانات الـ webhook للمراجعة
            payment.webhook_data = json.dumps(webhook_data)

            print(f"[{request_id}] 🔄 Status updated: {old_status} → {status}")

            result_data = {
                'payment_id': payment.id,
                'user_id': payment.user_id,
                'old_status': old_status,
                'new_status': status,
                'checkout_id': checkout_id
            }

            # 4. معالجة الدفع المكتمل
            if status == 'paid':
                print(f"[{request_id}] 💰 Processing successful payment...")

                payment.paid_at = datetime.utcnow()

                # استخراج metadata
                try:
                    if payment.chargily_response:
                        chargily_data = json.loads(payment.chargily_response)
                        metadata = chargily_data.get('metadata', {})
                    else:
                        metadata = webhook_data.get('metadata', {})

                    user_id = int(metadata.get('user_id', payment.user_id))
                    plan_id = int(metadata.get('plan_id', payment.plan_id))

                    print(f"[{request_id}] 📊 Metadata: user_id={user_id}, plan_id={plan_id}")

                except (ValueError, TypeError, KeyError) as e:
                    print(f"[{request_id}] ⚠️ Metadata parsing error: {e}")
                    user_id = payment.user_id
                    plan_id = payment.plan_id

                # 5. إنشاء الاشتراك
                print(f"[{request_id}] 🔄 Creating subscription...")
                subscription = self.create_subscription(user_id, plan_id, is_free_trial=False)

                if subscription:
                    payment.subscription_id = subscription.id
                    result_data['subscription_id'] = subscription.id
                    result_data['subscription_created'] = True

                    print(f"[{request_id}] ✅ Subscription created: ID={subscription.id}")

                    # 6. إرسال إشعار نجاح الدفع
                    try:
                        self.send_subscription_notification(
                            user_id,
                            subscription.id,
                            'renewed',
                            'تم تجديد اشتراكك بنجاح! يمكنك الآن الاستمتاع بجميع ميزات Ta9affi.'
                        )
                        result_data['notification_sent'] = True
                        print(f"[{request_id}] 📧 Notification sent successfully")

                    except Exception as e:
                        print(f"[{request_id}] ⚠️ Notification error: {e}")
                        result_data['notification_sent'] = False
                        result_data['notification_error'] = str(e)

                else:
                    error_msg = "Failed to create subscription"
                    print(f"[{request_id}] ❌ {error_msg}")
                    result_data['subscription_created'] = False
                    result_data['error'] = error_msg

            elif status == 'failed':
                print(f"[{request_id}] ❌ Payment failed")
                result_data['payment_failed'] = True

                # إرسال إشعار فشل الدفع
                try:
                    self.send_subscription_notification(
                        payment.user_id,
                        None,
                        'payment_failed',
                        'فشل في عملية الدفع. يرجى المحاولة مرة أخرى أو التواصل مع الدعم الفني.'
                    )
                    result_data['failure_notification_sent'] = True

                except Exception as e:
                    print(f"[{request_id}] ⚠️ Failure notification error: {e}")
                    result_data['failure_notification_sent'] = False

            elif status == 'canceled':
                print(f"[{request_id}] 🚫 Payment canceled")
                result_data['payment_canceled'] = True

            # 7. حفظ التغييرات
            print(f"[{request_id}] 💾 Saving changes to database...")
            db.session.commit()

            print(f"[{request_id}] ✅ Webhook processing completed successfully")
            return True, result_data

        except Exception as e:
            print(f"[{request_id}] 💥 Error processing webhook: {str(e)}")
            import traceback
            print(f"[{request_id}] Traceback: {traceback.format_exc()}")

            # التراجع عن التغييرات في حالة الخطأ
            try:
                db.session.rollback()
                print(f"[{request_id}] 🔄 Database rollback completed")
            except Exception as rollback_error:
                print(f"[{request_id}] ❌ Rollback error: {rollback_error}")

            return False, {
                'error': str(e),
                'checkout_id': webhook_data.get('checkout_id'),
                'status': webhook_data.get('status')
            }

    def check_expiring_subscriptions(self):
        """فحص الاشتراكات التي تقارب على الانتهاء"""
        # البحث عن الاشتراكات التي تنتهي خلال 7 أيام
        expiring_date = datetime.utcnow() + timedelta(days=7)
        expiring_subscriptions = Subscription.query.filter(
            Subscription.is_active == True,
            Subscription.end_date <= expiring_date,
            Subscription.end_date > datetime.utcnow()
        ).all()
        
        for subscription in expiring_subscriptions:
            # التحقق من عدم إرسال إشعار مسبق
            existing_notification = SubscriptionNotification.query.filter_by(
                user_id=subscription.user_id,
                subscription_id=subscription.id,
                notification_type='expiring_soon'
            ).first()
            
            if not existing_notification:
                self.send_subscription_notification(
                    subscription.user_id,
                    subscription.id,
                    'expiring_soon',
                    f'ينتهي اشتراكك خلال {subscription.days_remaining} أيام. يرجى التجديد لتجنب انقطاع الخدمة.'
                )

    def check_expired_subscriptions(self):
        """فحص الاشتراكات المنتهية وتعطيل الحسابات"""
        expired_subscriptions = Subscription.query.filter(
            Subscription.is_active == True,
            Subscription.end_date <= datetime.utcnow()
        ).all()
        
        for subscription in expired_subscriptions:
            # تعطيل الاشتراك
            subscription.is_active = False
            
            # تحديث حالة المستخدم
            user = subscription.user
            user.subscription_status = 'expired'
            
            # إرسال إشعار انتهاء الاشتراك
            self.send_subscription_notification(
                subscription.user_id,
                subscription.id,
                'expired',
                'انتهى اشتراكك. يرجى التجديد لاستعادة الوصول للميزات المدفوعة.'
            )
        
        db.session.commit()

    def send_subscription_notification(self, user_id, subscription_id, notification_type, message):
        """إرسال إشعار اشتراك"""
        # subscription_id يمكن أن يكون None في حالة الفترة التجريبية
        notification = SubscriptionNotification(
            user_id=user_id,
            subscription_id=subscription_id,
            notification_type=notification_type,
            message=message
        )

        db.session.add(notification)
        db.session.commit()

    def initialize_user_free_trial(self, user):
        """تفعيل الفترة التجريبية للمستخدم الجديد"""
        if user.role in [Role.TEACHER, Role.INSPECTOR] and not user.free_trial_end:
            user.initialize_free_trial()
            db.session.commit()

    def extend_subscription_days(self, user_id, days_to_add, admin_user_id, reason=""):
        """
        إضافة أيام للاشتراك الحالي أو الفترة التجريبية

        Args:
            user_id: معرف المستخدم
            days_to_add: عدد الأيام المراد إضافتها (1-300)
            admin_user_id: معرف الأدمن أو مدير المستخدمين الذي يقوم بالعملية
            reason: سبب الإضافة (اختياري)

        Returns:
            dict: نتيجة العملية مع التفاصيل
        """
        try:
            # التحقق من صحة البيانات
            if not isinstance(days_to_add, int) or days_to_add < 1 or days_to_add > 300:
                return {
                    'success': False,
                    'error': 'عدد الأيام يجب أن يكون بين 1 و 300'
                }

            # الحصول على المستخدم
            user = User.query.get(user_id)
            if not user:
                return {
                    'success': False,
                    'error': 'المستخدم غير موجود'
                }

            # التحقق من أن المستخدم مؤهل للاشتراك
            if user.role not in [Role.TEACHER, Role.INSPECTOR]:
                return {
                    'success': False,
                    'error': 'هذا المستخدم غير مؤهل للاشتراك'
                }

            # الحصول على المستخدم الأدمن
            admin_user = User.query.get(admin_user_id)
            if not admin_user or admin_user.role not in [Role.ADMIN, Role.USER_MANAGER]:
                return {
                    'success': False,
                    'error': 'غير مصرح بهذه العملية'
                }

            # التحقق من قيود مدير المستخدمين
            if admin_user.role == Role.USER_MANAGER and user.role == Role.ADMIN:
                return {
                    'success': False,
                    'error': 'مدير المستخدمين لا يمكنه تعديل اشتراكات الأدمن'
                }

            # تحديد نوع التمديد والتنفيذ
            extension_type = None
            old_end_date = None
            new_end_date = None

            # التحقق من وجود اشتراك نشط
            current_subscription = user.current_subscription

            if current_subscription:
                # تمديد الاشتراك المدفوع
                extension_type = 'paid_subscription'
                old_end_date = current_subscription.end_date
                new_end_date = old_end_date + timedelta(days=days_to_add)
                current_subscription.end_date = new_end_date

            elif user.free_trial_end and datetime.utcnow() <= user.free_trial_end:
                # تمديد الفترة التجريبية
                extension_type = 'free_trial'
                old_end_date = user.free_trial_end
                new_end_date = old_end_date + timedelta(days=days_to_add)
                user.free_trial_end = new_end_date

            elif user.free_trial_end and datetime.utcnow() > user.free_trial_end:
                # إعادة تفعيل الفترة التجريبية المنتهية
                extension_type = 'reactivated_trial'
                old_end_date = user.free_trial_end
                new_end_date = datetime.utcnow() + timedelta(days=days_to_add)
                user.free_trial_end = new_end_date
                user.subscription_status = 'trial'

            else:
                # إنشاء فترة تجريبية جديدة
                extension_type = 'new_trial'
                old_end_date = None
                new_end_date = datetime.utcnow() + timedelta(days=days_to_add)
                user.free_trial_end = new_end_date
                user.subscription_status = 'trial'

            # تسجيل العملية في السجل
            extension_log = SubscriptionExtensionLog(
                user_id=user_id,
                admin_user_id=admin_user_id,
                subscription_id=current_subscription.id if current_subscription else None,
                days_added=days_to_add,
                extension_type=extension_type,
                reason=reason,
                old_end_date=old_end_date,
                new_end_date=new_end_date
            )
            db.session.add(extension_log)

            # حفظ التغييرات
            db.session.commit()

            # إرسال إشعار للمستخدم
            notification_message = f"تم إضافة {days_to_add} يوم لاشتراكك من قبل الإدارة"
            if reason:
                notification_message += f" - السبب: {reason}"

            self.send_subscription_notification(
                user_id,
                current_subscription.id if current_subscription else None,
                'extended',
                notification_message
            )

            return {
                'success': True,
                'extension_type': extension_type,
                'days_added': days_to_add,
                'old_end_date': old_end_date.isoformat() if old_end_date else None,
                'new_end_date': new_end_date.isoformat(),
                'admin_user': admin_user.username,
                'reason': reason
            }

        except Exception as e:
            db.session.rollback()
            return {
                'success': False,
                'error': f'حدث خطأ أثناء تمديد الاشتراك: {str(e)}'
            }

# إنشاء مثيل مدير الاشتراكات
subscription_manager = SubscriptionManager()
