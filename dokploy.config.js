// إعداد<PERSON>ت Dokploy لـ Ta9affi
export default {
  // معلومات المشروع
  name: 'ta9affi',
  description: 'نظام إدارة التقدم التعليمي',
  
  // إعدادات التطبيق
  apps: [
    {
      name: 'ta9affi-app',
      type: 'docker-compose',
      source: {
        type: 'docker-compose',
        file: 'docker-compose.prod.yml'
      },
      
      // إعدادات النطاق
      domains: [
        {
          host: 'ta9affi.com',
          port: 80,
          ssl: true,
          certificateType: 'letsencrypt'
        }
      ],
      
      // متغيرات البيئة
      env: {
        FLASK_ENV: 'production',
        POSTGRES_PASSWORD: 'ta9affi_secure_password_2024',
        SECRET_KEY: 'ta9affi-production-secret-key-2024-very-secure',
        CHARGILY_PUBLIC_KEY: 'live_pk_2pD7cep2GCAuBHDxXXegTAkrOLBrnD59tkyZeGCk',
        CHARGILY_SECRET_KEY: 'live_sk_914RIuLl0mtEjHhSvhylpDMnPiadv74Gp0DTiNpU',
        CHARGILY_WEBHOOK_URL: 'https://ta9affi.com/chargily-webhook'
      },
      
      // إعدادات الصحة
      healthCheck: {
        enabled: true,
        path: '/health',
        interval: 30,
        timeout: 10,
        retries: 3
      },
      
      // إعدادات الموارد
      resources: {
        limits: {
          memory: '2G',
          cpus: '1.0'
        },
        reservations: {
          memory: '1G',
          cpus: '0.5'
        }
      }
    }
  ],
  
  // إعدادات قاعدة البيانات
  databases: [
    {
      name: 'ta9affi-postgres',
      type: 'postgres',
      version: '15',
      env: {
        POSTGRES_DB: 'ta9affi_production',
        POSTGRES_USER: 'ta9affi_user',
        POSTGRES_PASSWORD: 'ta9affi_secure_password_2024'
      }
    },
    {
      name: 'ta9affi-redis',
      type: 'redis',
      version: '7'
    }
  ]
}
