#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعدادات الإنتاج لـ Ta9affi - محسنة لمئات الآلاف من المستخدمين
"""

import os
from datetime import timedelta

class ProductionConfig:
    """إعدادات الإنتاج المحسنة"""
    
    # إعدادات أساسية
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'ta9affi-production-secret-key-2024-very-secure'
    DEBUG = False
    TESTING = False
    
    # إعدادات قاعدة البيانات PostgreSQL
    POSTGRES_USER = os.environ.get('POSTGRES_USER', 'ta9affi_user')
    POSTGRES_PASSWORD = os.environ.get('POSTGRES_PASSWORD', 'ta9affi_secure_password_2024')
    POSTGRES_HOST = os.environ.get('POSTGRES_HOST', 'postgres')
    POSTGRES_PORT = os.environ.get('POSTGRES_PORT', '5432')
    POSTGRES_DB = os.environ.get('POSTGRES_DB', 'ta9affi_production')
    
    SQLALCHEMY_DATABASE_URI = f"postgresql://{POSTGRES_USER}:{POSTGRES_PASSWORD}@{POSTGRES_HOST}:{POSTGRES_PORT}/{POSTGRES_DB}"
    
    # تحسينات قاعدة البيانات للأداء العالي
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_size': 50,                    # عدد الاتصالات الأساسية
        'pool_timeout': 30,                 # مهلة انتظار الاتصال
        'pool_recycle': 3600,              # إعادة تدوير الاتصالات كل ساعة
        'max_overflow': 100,               # اتصالات إضافية عند الحاجة
        'pool_pre_ping': True,             # فحص الاتصالات قبل الاستخدام
        'connect_args': {
            'connect_timeout': 10,
            'application_name': 'ta9affi_production'
        }
    }
    
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_RECORD_QUERIES = False      # تعطيل تسجيل الاستعلامات في الإنتاج
    
    # إعدادات Redis للتخزين المؤقت
    REDIS_HOST = os.environ.get('REDIS_HOST', 'redis')
    REDIS_PORT = int(os.environ.get('REDIS_PORT', 6379))
    REDIS_PASSWORD = os.environ.get('REDIS_PASSWORD', '')
    REDIS_DB = int(os.environ.get('REDIS_DB', 0))
    
    REDIS_URL = f"redis://:{REDIS_PASSWORD}@{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}" if REDIS_PASSWORD else f"redis://{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}"
    
    # إعدادات التخزين المؤقت
    CACHE_TYPE = 'redis'
    CACHE_REDIS_URL = REDIS_URL
    CACHE_DEFAULT_TIMEOUT = 300            # 5 دقائق
    CACHE_KEY_PREFIX = 'ta9affi:'
    
    # إعدادات الجلسات
    SESSION_TYPE = 'redis'
    SESSION_REDIS = REDIS_URL
    SESSION_PERMANENT = False
    SESSION_USE_SIGNER = True
    SESSION_KEY_PREFIX = 'ta9affi:session:'
    PERMANENT_SESSION_LIFETIME = timedelta(hours=24)
    
    # إعدادات الأمان
    WTF_CSRF_ENABLED = True
    WTF_CSRF_TIME_LIMIT = 3600            # ساعة واحدة
    
    # إعدادات الملفات المرفوعة
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16 MB
    UPLOAD_FOLDER = '/app/uploads'
    
    # إعدادات البريد الإلكتروني
    MAIL_SERVER = os.environ.get('MAIL_SERVER', 'smtp.gmail.com')
    MAIL_PORT = int(os.environ.get('MAIL_PORT', 587))
    MAIL_USE_TLS = True
    MAIL_USE_SSL = False
    MAIL_USERNAME = os.environ.get('MAIL_USERNAME')
    MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD')
    MAIL_DEFAULT_SENDER = os.environ.get('MAIL_DEFAULT_SENDER', '<EMAIL>')
    
    # إعدادات Chargily
    CHARGILY_PUBLIC_KEY = os.environ.get('CHARGILY_PUBLIC_KEY', 'live_pk_2pD7cep2GCAuBHDxXXegTAkrOLBrnD59tkyZeGCk')
    CHARGILY_SECRET_KEY = os.environ.get('CHARGILY_SECRET_KEY', 'live_sk_914RIuLl0mtEjHhSvhylpDMnPiadv74Gp0DTiNpU')
    CHARGILY_WEBHOOK_URL = os.environ.get('CHARGILY_WEBHOOK_URL', 'https://ta9affi.com/chargily-webhook')
    
    # إعدادات Rate Limiting
    RATELIMIT_STORAGE_URL = REDIS_URL
    RATELIMIT_DEFAULT = "1000 per hour"    # حد أعلى للطلبات
    
    # إعدادات الأداء
    SEND_FILE_MAX_AGE_DEFAULT = 31536000   # سنة واحدة للملفات الثابتة
    
    # إعدادات التسجيل
    LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO')
    LOG_FILE = '/app/logs/ta9affi.log'
    
    # إعدادات المنطقة الزمنية
    TIMEZONE = 'Africa/Algiers'
    
    # إعدادات الضغط
    COMPRESS_MIMETYPES = [
        'text/html',
        'text/css',
        'text/xml',
        'application/json',
        'application/javascript'
    ]
    COMPRESS_LEVEL = 6
    COMPRESS_MIN_SIZE = 500

class DevelopmentConfig:
    """إعدادات التطوير"""
    
    SECRET_KEY = 'dev-secret-key'
    DEBUG = True
    TESTING = False
    
    # SQLite للتطوير
    SQLALCHEMY_DATABASE_URI = 'sqlite:///ta9affi.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # بدون Redis في التطوير
    CACHE_TYPE = 'simple'
    
    # إعدادات أخرى مبسطة للتطوير
    WTF_CSRF_ENABLED = True
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024
    
    # Chargily للتطوير
    CHARGILY_PUBLIC_KEY = 'live_pk_2pD7cep2GCAuBHDxXXegTAkrOLBrnD59tkyZeGCk'
    CHARGILY_SECRET_KEY = 'live_sk_914RIuLl0mtEjHhSvhylpDMnPiadv74Gp0DTiNpU'
    CHARGILY_WEBHOOK_URL = 'https://ta9affi.com/chargily-webhook'

class TestingConfig:
    """إعدادات الاختبار"""
    
    SECRET_KEY = 'test-secret-key'
    DEBUG = False
    TESTING = True
    
    # قاعدة بيانات في الذاكرة للاختبار
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # تعطيل CSRF في الاختبار
    WTF_CSRF_ENABLED = False
    
    # تخزين مؤقت بسيط
    CACHE_TYPE = 'simple'

# اختيار الإعدادات حسب البيئة
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}

def get_config():
    """الحصول على إعدادات البيئة الحالية"""
    env = os.environ.get('FLASK_ENV', 'development')
    return config.get(env, config['default'])
