{% extends 'base.html' %}

{% block content %}
<!-- شريط الأخبار والتحديثات -->
{% if active_news %}
<div class="news-ticker-container">
    <div class="news-ticker">
        <span class="news-label">
            <i class="fas fa-bullhorn me-2"></i>
            آخر المستجدات:
        </span>
        <div class="news-display">
            {% for news in active_news %}
            <div class="news-item" data-index="{{ loop.index0 }}" {% if loop.first %}style="display: block;" {% else
                %}style="display: none;" {% endif %}>
                <strong>{{ news.title }}</strong> - {{ news.content }}
            </div>
            {% endfor %}
        </div>
    </div>
</div>
{% endif %}
<div class="row justify-content-center">
    <div class="col-md-10">
        <div class="card shadow-lg border-0 rounded-lg mt-5 main-card slide-in">
            <div class="card-header bg-primary text-white">
                <h3 class="text-center font-weight-light my-2">
                    <i class="fas fa-graduation-cap animated-icon bounce-icon me-2"></i>
                    مرحباً بك في نظام Ta9affi
                    <i class="fas fa-star animated-icon pulse-icon ms-2"></i>
                </h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <img src="{{ url_for('static', filename='img/education.svg') }}" alt="Education"
                            class="img-fluid main-image">
                    </div>
                    <div class="col-md-6">
                        <h4 class="mb-4">نظام إدارة البرنامج السنوي للتدريس</h4>
                        <p class="lead">
                            منصة متكاملة لإدارة ومتابعة البرنامج السنوي للتدريس في التعليم الإبتدائي في الجزائر.
                        </p>
                        <p>
                            نظام مصمم خصيصاً للأساتذة لإدارة برنامجهم السنوي وتتبع تقدمهم في التدريس بكل سهولة ويسر.
                        </p>
                        <div class="d-grid gap-2 d-md-flex justify-content-md-start mt-4">
                            {% if current_user.is_authenticated %}
                            <a href="{{ url_for('dashboard') }}"
                                class="btn btn-primary btn-lg px-4 me-md-2 btn-hover-effect">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                لوحة التحكم
                            </a>
                            {% if current_user.role == 'teacher' %}
                            <a href="{{ url_for('teaching_program') }}"
                                class="btn btn-success btn-lg px-4 me-md-2 btn-hover-effect">
                                <i class="fas fa-plus-circle me-2"></i>
                                إضافة تقدم
                            </a>
                            {% endif %}
                            {% else %}
                            <a href="{{ url_for('login') }}"
                                class="btn btn-primary btn-lg px-4 me-md-2 btn-hover-effect">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                تسجيل الدخول
                            </a>
                            <a href="{{ url_for('register') }}"
                                class="btn btn-outline-secondary btn-lg px-4 btn-hover-effect">
                                <i class="fas fa-user-plus me-2"></i>
                                طلب حساب جديد
                            </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-5 justify-content-center">
    <div class="col-md-8">
        <div class="card home-card slide-in scale-on-hover">
            <div class="card-body text-center">
                <i class="fas fa-chalkboard-teacher fa-4x mb-4 home-icon animated-icon"></i>
                <h4 class="card-title color-transition mb-3">للأساتذة</h4>
                <p class="card-text lead">
                    إدارة شاملة للبرنامج السنوي وتسجيل التقدم حسب جدول التدريس الخاص بك.
                    نظام سهل ومرن يساعدك على تنظيم عملك التعليمي بكفاءة عالية.
                </p>
            </div>
        </div>
    </div>
</div>

<!-- قسم الميزات المتاحة للأستاذ -->
<div class="row mt-5">
    <div class="col-md-12">
        <h3 class="text-center mb-4">الميزات المتاحة للأستاذ</h3>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="card h-100 home-card fade-in scale-on-hover">
            <div class="card-body text-center">
                <i class="fas fa-calendar-alt fa-2x mb-3 home-icon animated-icon rotate-icon"></i>
                <h6 class="card-title color-transition">إدارة الجدول الزمني</h6>
                <p class="card-text small">تنظيم وإدارة جدولك الزمني للتدريس بسهولة</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card h-100 home-card fade-in scale-on-hover">
            <div class="card-body text-center">
                <i class="fas fa-book-open fa-2x mb-3 home-icon animated-icon"></i>
                <h6 class="card-title color-transition">تحضير الدروس</h6>
                <p class="card-text small">تحضير وتنظيم دروسك وفقاً للبرنامج السنوي</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card h-100 home-card fade-in scale-on-hover">
            <div class="card-body text-center">
                <i class="fas fa-chart-line fa-2x mb-3 home-icon animated-icon"></i>
                <h6 class="card-title color-transition">تتبع التقدم</h6>
                <p class="card-text small">متابعة تقدمك في تنفيذ البرنامج السنوي</p>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-4">
        <div class="card h-100 home-card fade-in scale-on-hover">
            <div class="card-body text-center">
                <i class="fas fa-database fa-2x mb-3 home-icon animated-icon pulse-icon"></i>
                <h6 class="card-title color-transition">قاعدة البيانات</h6>
                <p class="card-text small">الوصول لقاعدة بيانات المناهج والمواد التعليمية</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card h-100 home-card fade-in scale-on-hover">
            <div class="card-body text-center">
                <i class="fas fa-print fa-2x mb-3 home-icon animated-icon"></i>
                <h6 class="card-title color-transition">طباعة التقارير</h6>
                <p class="card-text small">طباعة تقارير التقدم والجداول الزمنية</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card h-100 home-card fade-in scale-on-hover">
            <div class="card-body text-center">
                <i class="fas fa-bell fa-2x mb-3 home-icon animated-icon shake-icon"></i>
                <h6 class="card-title color-transition">الإشعارات</h6>
                <p class="card-text small">تلقي إشعارات مهمة حول البرنامج والتحديثات</p>
            </div>
        </div>
    </div>
</div>

<!-- قسم شروط الاستخدام وسياسة الخصوصية -->
<div class="row mt-5">
    <div class="col-md-12">
        <div class="card border-0 bg-light">
            <div class="card-body text-center py-4">
                <h5 class="text-muted mb-3">
                    <i class="fas fa-shield-alt me-2"></i>
                    معلومات مهمة
                </h5>
                <p class="text-muted mb-3">
                    للاستفادة من خدماتنا بأمان وشفافية، يرجى الاطلاع على شروط الاستخدام وسياسة الخصوصية
                </p>
                <a href="{{ url_for('terms_and_privacy') }}" class="btn btn-outline-primary">
                    <i class="fas fa-file-contract me-2"></i>
                    شروط الاستخدام وسياسة الخصوصية
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    /* شريط الأخبار */
    .news-ticker-container {
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
        padding: 0;
        margin-bottom: 20px;
        box-shadow: 0 2px 10px rgba(0, 123, 255, 0.3);
        overflow: hidden;
        position: relative;
    }

    .news-ticker {
        height: 50px;
        display: flex;
        align-items: center;
        position: relative;
        padding: 0 20px;
    }

    .news-label {
        background: rgba(255, 255, 255, 0.2);
        padding: 8px 15px;
        border-radius: 20px;
        font-weight: bold;
        margin-right: 20px;
        flex-shrink: 0;
        z-index: 2;
    }

    .news-display {
        flex: 1;
        position: relative;
        height: 100%;
        display: flex;
        align-items: center;
    }

    .news-item {
        position: absolute;
        left: 0;
        right: 0;
        font-size: 14px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        padding-right: 20px;
        opacity: 0;
        transform: translateY(100%) rotateX(-90deg);
        transition: all 0.6s ease-in-out;
    }

    .news-item.active {
        opacity: 1;
        transform: translateY(0) rotateX(0deg);
    }

    .news-item strong {
        color: #ffd700;
        margin-right: 5px;
    }

    /* تأثير flip up للدخول */
    @keyframes flip-in {
        0% {
            transform: translateY(100%) rotateX(-90deg);
            opacity: 0;
        }

        50% {
            transform: translateY(0) rotateX(-45deg);
            opacity: 0.7;
        }

        100% {
            transform: translateY(0) rotateX(0deg);
            opacity: 1;
        }
    }

    /* تأثير flip up للخروج */
    @keyframes flip-out {
        0% {
            transform: translateY(0) rotateX(0deg);
            opacity: 1;
        }

        50% {
            transform: translateY(-50%) rotateX(45deg);
            opacity: 0.7;
        }

        100% {
            transform: translateY(-100%) rotateX(90deg);
            opacity: 0;
        }
    }

    .news-item.flip-in {
        animation: flip-in 0.6s ease-in-out forwards;
    }

    .news-item.flip-out {
        animation: flip-out 0.6s ease-in-out forwards;
    }

    /* تجاوب مع الشاشات الصغيرة */
    @media (max-width: 768px) {
        .news-ticker-container {
            margin-bottom: 15px;
        }

        .news-ticker {
            height: 40px;
        }

        .news-item {
            font-size: 12px;
            margin-right: 30px;
        }

        .news-label {
            padding: 6px 12px;
            font-size: 12px;
            margin-right: 15px;
        }
    }
</style>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/animations.js') }}"></script>

<script>
    // شريط الأخبار مع تأثير flip-up وتوقيت 4 ثوانٍ
    document.addEventListener('DOMContentLoaded', function () {
        const newsItems = document.querySelectorAll('.news-item');

        if (newsItems.length === 0) return;

        let currentIndex = 0;
        let isTransitioning = false;
        let isPaused = false;

        // إظهار الخبر الأول
        newsItems[0].classList.add('active');

        function showNextNews() {
            if (isPaused || isTransitioning) return;

            if (newsItems.length <= 1) return; // إذا كان هناك خبر واحد فقط

            isTransitioning = true;

            const currentItem = newsItems[currentIndex];
            const nextIndex = (currentIndex + 1) % newsItems.length;
            const nextItem = newsItems[nextIndex];

            // إخفاء الخبر الحالي بتأثير flip-out
            currentItem.classList.add('flip-out');
            currentItem.classList.remove('active');

            // بعد انتهاء تأثير الخروج، إظهار الخبر التالي
            setTimeout(() => {
                currentItem.classList.remove('flip-out');
                currentItem.style.display = 'none';

                // إظهار الخبر التالي بتأثير flip-in
                nextItem.style.display = 'block';
                nextItem.classList.add('flip-in');

                setTimeout(() => {
                    nextItem.classList.remove('flip-in');
                    nextItem.classList.add('active');

                    currentIndex = nextIndex;
                    isTransitioning = false;
                }, 600); // مدة تأثير flip-in

            }, 600); // مدة تأثير flip-out
        }

        // بدء التنقل التلقائي كل 4 ثوانٍ
        let newsInterval = setInterval(showNextNews, 4000);

        // إيقاف التنقل عند hover
        const newsTicker = document.querySelector('.news-ticker-container');
        if (newsTicker) {
            newsTicker.addEventListener('mouseenter', function () {
                isPaused = true;
                clearInterval(newsInterval);
            });

            newsTicker.addEventListener('mouseleave', function () {
                isPaused = false;
                newsInterval = setInterval(showNextNews, 4000);
            });
        }

        // تنظيف عند إغلاق الصفحة
        window.addEventListener('beforeunload', function () {
            clearInterval(newsInterval);
        });
    });
</script>
{% endblock %}