# إعدادات Gunicorn للإنتاج - Ta9affi
import multiprocessing
import os

# إعدادات الخادم
bind = "0.0.0.0:8000"
backlog = 2048

# عدد العمال
workers = 4
worker_class = "gevent"
worker_connections = 1000

# إعدادات المهلة الزمنية
timeout = 120
keepalive = 5

# إعدادات الأمان
max_requests = 1000
max_requests_jitter = 100

# السجلات
accesslog = "-"
errorlog = "-"
loglevel = "info"

# إعدادات الأداء
preload_app = True

# متغيرات البيئة
raw_env = [
    'FLASK_ENV=production',
    'PYTHONPATH=/app',
]

# دوال التحكم
def when_ready(server):
    server.log.info("🚀 Ta9affi Server is ready on %s", server.address)

def on_starting(server):
    server.log.info("🎯 Ta9affi Production Server Starting...")
    server.log.info("📊 Workers: %d, Class: %s", workers, worker_class)

def on_exit(server):
    server.log.info("👋 Ta9affi Server Shutting Down...")

# تحسينات Docker
if os.path.exists('/.dockerenv'):
    bind = "0.0.0.0:8080"
    tmp_upload_dir = "/tmp"
    worker_tmp_dir = "/tmp"
    print("🐳 Running in Docker container - optimizations applied")








