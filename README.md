# Ta9affi - نظام إدارة التعليم الابتدائي

نظام شامل لإدارة التعليم الابتدائي في الجزائر، يوفر أدوات متقدمة لإدارة المناهج والمستويات التعليمية مع نظام إدارة أدوار متطور.

## 🌟 المميزات الرئيسية

### إدارة المحتوى التعليمي
- ✅ إدارة المستويات التعليمية (السنة الأولى إلى الخامسة ابتدائي)
- ✅ إدارة المواد الدراسية والميادين (8 مواد أساسية)
- ✅ إدارة الموارد المعرفية والكفاءات المستهدفة
- ✅ قواعد بيانات منفصلة لكل مستوى تعليمي
- ✅ استيراد وتصدير البيانات التعليمية

### نظام إدارة الأدوار المتطور
- 👑 **المدير (Admin)**: صلاحيات كاملة + إنشاء حسابات مدير المستخدمين + تفعيل/تعطيل جماعي للمعلمين
- 👥 **مدير المستخدمين (User Manager)**: إدارة محدودة للمستخدمين + تفعيل/تعطيل فردي
- 🔍 **المفتش (Inspector)**: مراقبة وتقييم الأداء
- 👨‍🏫 **المعلم (Teacher)**: إدارة المحتوى التعليمي

### واجهة المستخدم
- 🇩🇿 واجهة مستخدم باللغة العربية
- 📱 تصميم متجاوب مع Bootstrap 5
- 🎨 واجهات مخصصة لكل دور
- 📊 لوحات تحكم تفاعلية مع إحصائيات

## 🛠️ متطلبات النظام

- Python 3.8 أو أحدث
- Flask 2.3+
- SQLAlchemy 2.0+
- Flask-Login
- Flask-WTF
- Bootstrap 5

## 🚀 التثبيت والتشغيل

### 1. تحضير البيئة
```bash
# تأكد من تثبيت Python 3.8+
python --version

# تفعيل البيئة الافتراضية
venv\Scripts\activate  # Windows
# أو
source venv/bin/activate  # Linux/Mac
```

### 2. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 3. تشغيل التطبيق
```bash
# الطريقة الأولى
python run.py

# الطريقة الثانية (Windows)
run.bat
```

### 4. الوصول للتطبيق
افتح المتصفح وانتقل إلى: `http://localhost:5000`

## 👥 حسابات تجريبية

| الدور | اسم المستخدم | كلمة المرور | الصلاحيات |
|-------|---------------|-------------|-----------|
| Admin | `<EMAIL>` | `admin123` | صلاحيات كاملة |
| User Manager | `<EMAIL>` | `manager123` | إدارة محدودة للمستخدمين |
| Inspector | `<EMAIL>` | `inspector123` | مراقبة وتقييم |
| Teacher | `<EMAIL>` | `teacher123` | إدارة المحتوى |

## 📁 هيكل المشروع المنظم

```
Ta9affi/
├── 📄 app.py                          # التطبيق الرئيسي
├── 📄 models_new.py                   # نماذج قاعدة البيانات
├── 📄 requirements.txt                # متطلبات Python
├── 📄 run.py                         # ملف تشغيل التطبيق
├── 📄 run.bat                        # ملف تشغيل Windows
├── 📄 materials_data_updated.json     # بيانات الموارد المعرفية
├── 📄 educational_data.xlsx          # بيانات تعليمية
├── 📄 rebuild_db.py                  # إعادة بناء قاعدة البيانات
├── 📁 instance/                      # قواعد البيانات
│   ├── ta9affi_new.db               # قاعدة البيانات الرئيسية
│   └── ta9affi.db                   # قاعدة بيانات احتياطية
├── 📁 templates/                     # قوالب HTML
│   ├── admin_dashboard.html         # لوحة تحكم المدير
│   ├── user_manager_dashboard.html  # لوحة تحكم مدير المستخدمين
│   ├── inspector_dashboard.html     # لوحة تحكم المفتش
│   ├── teacher_dashboard.html       # لوحة تحكم المعلم
│   └── ...                         # باقي القوالب
├── 📁 static/                       # الملفات الثابتة
│   ├── css/                        # ملفات التنسيق
│   ├── js/                         # ملفات JavaScript
│   ├── img/                        # الصور
│   └── exports/                    # ملفات التصدير
├── 📁 database_backup/              # نسخ احتياطية
└── 📁 venv/                        # البيئة الافتراضية
```

## 🔧 ملفات الصيانة

- **restore_educational_data.py**: استعادة البيانات التعليمية
- **verify_educational_data.py**: التحقق من سلامة البيانات
- **cleanup_project.py**: تنظيف المشروع من الملفات غير الضرورية

## 📊 البيانات التعليمية

### المواد الدراسية (8 مواد)
1. اللغة العربية
2. الرياضيات
3. التربية العلمية والتكنولوجية
4. التربية المدنية
5. التاريخ
6. الجغرافيا
7. التربية الإسلامية
8. التربية البدنية والرياضية

### المستويات التعليمية
- السنة الأولى ابتدائي
- السنة الثانية ابتدائي
- السنة الثالثة ابتدائي
- السنة الرابعة ابتدائي
- السنة الخامسة ابتدائي

## 🔐 الأمان والصلاحيات

- نظام تسجيل دخول آمن
- تشفير كلمات المرور
- فصل الصلاحيات حسب الأدوار
- حماية من الوصول غير المصرح به

## 🆘 الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- راجع ملف PROJECT_STRUCTURE.md للتفاصيل التقنية
- تحقق من ملفات الصيانة في حالة مشاكل البيانات

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف LICENSE.txt للمزيد من التفاصيل.

"# Ta9affi1.14" 
"# Ta9affi1.16" 
