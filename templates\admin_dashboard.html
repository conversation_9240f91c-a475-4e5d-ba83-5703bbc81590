{% extends 'base.html' %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h2 class="mb-4">لوحة تحكم الإدارة</h2>
    </div>
</div>

<div class="row">
    <!-- بطاقة المستخدمين المتصلين -->
    <div class="col-xl-3 col-md-6">
        <div class="card bg-success text-white mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-circle text-light me-1" style="font-size: 0.8rem;"></i>
                        المستخدمون المتصلون
                    </div>
                    <div class="h3" id="online-users-count">{{ online_users_count }}</div>
                </div>
            </div>
            <div class="card-footer d-flex align-items-center justify-content-between">
                <span class="small text-white">متصل الآن</span>
                <div class="small text-white"><i class="fas fa-wifi"></i></div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card bg-primary text-white mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>المفتشون</div>
                    <div class="h3">{{ inspectors|length }}</div>
                </div>
            </div>
            <div class="card-footer d-flex align-items-center justify-content-between">
                <a class="small text-white stretched-link" href="#" data-bs-toggle="modal"
                    data-bs-target="#inspectorsModal">عرض التفاصيل</a>
                <div class="small text-white"><i class="fas fa-angle-left"></i></div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card bg-info text-white mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>مديرو المستخدمين</div>
                    <div class="h3">{{ user_managers|length }}</div>
                </div>
            </div>
            <div class="card-footer d-flex align-items-center justify-content-between">
                <a class="small text-white stretched-link" href="#" data-bs-toggle="modal"
                    data-bs-target="#userManagersModal">عرض التفاصيل</a>
                <div class="small text-white"><i class="fas fa-angle-left"></i></div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card bg-success text-white mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>المستويات التعليمية</div>
                    <div class="h3">5</div>
                </div>
            </div>
            <div class="card-footer d-flex align-items-center justify-content-between">
                <a class="small text-white stretched-link" href="#" data-bs-toggle="modal"
                    data-bs-target="#levelsModal">عرض التفاصيل</a>
                <div class="small text-white"><i class="fas fa-angle-left"></i></div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card bg-warning text-white mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>المواد الدراسية</div>
                    <div class="h3">
                        {% set total_subjects = 0 %}
                        {% for level_name, level_data in subjects_data.items() %}
                        {% set total_subjects = total_subjects + level_data.subjects|length %}
                        {% endfor %}
                        {{ total_subjects }}
                    </div>
                </div>
            </div>
            <div class="card-footer d-flex align-items-center justify-content-between">
                <a class="small text-white stretched-link" href="#" data-bs-toggle="modal"
                    data-bs-target="#subjectsModal">عرض التفاصيل</a>
                <div class="small text-white"><i class="fas fa-angle-left"></i></div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card bg-danger text-white mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>الكفاءات المستهدفة</div>
                    <div class="h3">150+</div>
                </div>
            </div>
            <div class="card-footer d-flex align-items-center justify-content-between">
                <a class="small text-white stretched-link" href="#" data-bs-toggle="modal"
                    data-bs-target="#competenciesModal">عرض التفاصيل</a>
                <div class="small text-white"><i class="fas fa-angle-left"></i></div>
            </div>
        </div>
    </div>
</div>

<!-- روابط سريعة للإدارة -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-tools me-2"></i>
                    أدوات الإدارة السريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('manage_inspectors') }}"
                            class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center">
                            <i class="fas fa-users-cog fa-2x mb-2"></i>
                            <span>إدارة المفتشين</span>
                            <small class="text-muted">تعيين الأساتذة للمفتشين</small>
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('manage_databases') }}"
                            class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center">
                            <i class="fas fa-database fa-2x mb-2"></i>
                            <span>إدارة قواعد البيانات</span>
                            <small class="text-muted">استيراد وإدارة المناهج</small>
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="#"
                            class="btn btn-outline-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center">
                            <i class="fas fa-chart-line fa-2x mb-2"></i>
                            <span>التقارير</span>
                            <small class="text-muted">تقارير شاملة للنظام</small>
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="#"
                            class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center">
                            <i class="fas fa-cog fa-2x mb-2"></i>
                            <span>إعدادات النظام</span>
                            <small class="text-muted">إعدادات عامة</small>
                        </a>
                    </div>
                </div>

                <!-- صف جديد للميزات المتقدمة -->
                <div class="row mt-3">
                    <div class="col-md-3 mb-3">
                        <button type="button"
                            class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center"
                            data-bs-toggle="modal" data-bs-target="#createUserManagerModal">
                            <i class="fas fa-user-plus fa-2x mb-2"></i>
                            <span>إنشاء مدير مستخدمين</span>
                            <small class="text-muted">إنشاء حساب مدير مستخدمين جديد</small>
                        </button>
                    </div>
                    <div class="col-md-3 mb-3">
                        <form method="POST" action="{{ url_for('admin_activate_all_teachers') }}" style="height: 100%;"
                            onsubmit="return confirm('هل أنت متأكد من تفعيل جميع حسابات المعلمين؟')">
                            <button type="submit"
                                class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center">
                                <i class="fas fa-user-check fa-2x mb-2"></i>
                                <span>تفعيل جميع المعلمين</span>
                                <small class="text-muted">تفعيل جميع حسابات المعلمين دفعة واحدة</small>
                            </button>
                        </form>
                    </div>
                    <div class="col-md-3 mb-3">
                        <form method="POST" action="{{ url_for('admin_deactivate_all_teachers') }}"
                            style="height: 100%;"
                            onsubmit="return confirm('هل أنت متأكد من تعطيل جميع حسابات المعلمين؟')">
                            <button type="submit"
                                class="btn btn-outline-danger w-100 h-100 d-flex flex-column align-items-center justify-content-center">
                                <i class="fas fa-user-times fa-2x mb-2"></i>
                                <span>تعطيل جميع المعلمين</span>
                                <small class="text-muted">تعطيل جميع حسابات المعلمين دفعة واحدة</small>
                            </button>
                        </form>
                    </div>
                    <div class="col-md-3 mb-3">
                        <button type="button"
                            class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center"
                            data-bs-toggle="modal" data-bs-target="#roleSettingsModal">
                            <i class="fas fa-user-cog fa-2x mb-2"></i>
                            <span>إدارة الأدوار</span>
                            <small class="text-muted">تحكم في الأدوار المتاحة للتسجيل</small>
                        </button>
                    </div>
                </div>

                <!-- صف إضافي للميزات الأخرى -->
                <div class="row mt-3">
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('users_list') }}"
                            class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center">
                            <i class="fas fa-users fa-2x mb-2"></i>
                            <span>قائمة المستخدمين</span>
                            <small class="text-muted">عرض وإدارة جميع المستخدمين</small>
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('admin_advanced') }}"
                            class="btn btn-outline-secondary w-100 h-100 d-flex flex-column align-items-center justify-content-center">
                            <i class="fas fa-users-cog fa-2x mb-2"></i>
                            <span>إدارة متقدمة</span>
                            <small class="text-muted">أدوات إدارة متقدمة</small>
                        </a>
                    </div>
                    {% if rate_limiting_enabled %}
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('admin_rate_limit.dashboard') }}"
                            class="btn btn-outline-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center">
                            <i class="fas fa-shield-alt fa-2x mb-2"></i>
                            <span>إدارة حدود النظام</span>
                            <small class="text-muted">تحكم في حدود الإضافة والحذف</small>
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- صف إضافي للإحصائيات الجديدة -->
<div class="row">
    <div class="col-xl-3 col-md-6">
        <div class="card bg-secondary text-white mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>حسابات الإدارة</div>
                    <div class="h3">{{ admins|length }}</div>
                </div>
            </div>
            <div class="card-footer d-flex align-items-center justify-content-between">
                <a class="small text-white stretched-link" href="#" data-bs-toggle="modal"
                    data-bs-target="#adminsModal">عرض التفاصيل</a>
                <div class="small text-white"><i class="fas fa-angle-left"></i></div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card bg-warning text-white mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>حسابات معطلة</div>
                    <div class="h3">{{ pending_users|length }}</div>
                </div>
            </div>
            <div class="card-footer d-flex align-items-center justify-content-between">
                <a class="small text-white stretched-link" href="#" data-bs-toggle="modal"
                    data-bs-target="#pendingUsersModal">عرض التفاصيل</a>
                <div class="small text-white"><i class="fas fa-angle-left"></i></div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card bg-dark text-white mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>إنشاء حساب جديد</div>
                    <div class="h3"><i class="fas fa-plus animated-icon pulse-icon"></i></div>
                </div>
            </div>
            <div class="card-footer d-flex align-items-center justify-content-between">
                <a class="small text-white stretched-link" href="#" data-bs-toggle="modal"
                    data-bs-target="#createUserModal">إنشاء حساب</a>
                <div class="small text-white"><i class="fas fa-angle-left"></i></div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header" data-bs-toggle="collapse" data-bs-target="#inspectorsCollapse"
                aria-expanded="false" aria-controls="inspectorsCollapse" style="cursor: pointer;">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-user-graduate me-1"></i>
                        المفتشون ({{ inspectors|length }})
                    </div>
                    <i class="fas fa-chevron-down collapse-icon"></i>
                </div>
            </div>
            <div class="collapse" id="inspectorsCollapse">
                <div class="card-body">
                    <!-- أداة البحث -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" class="form-control" id="inspectorSearch"
                                    placeholder="ابحث عن مفتش (الاسم أو البريد الإلكتروني)..."
                                    onkeyup="filterInspectors()">
                                <button class="btn btn-outline-secondary" type="button"
                                    onclick="clearInspectorSearch()">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <small class="text-muted">
                                <span id="inspectorCount">{{ inspectors|length }}</span> مفتش
                                <span id="inspectorFilteredCount" style="display: none;">
                                    | <span id="inspectorFilteredNumber">0</span> مطابق للبحث
                                </span>
                            </small>
                        </div>
                    </div>

                    <div class="table-responsive" style="max-height: 500px; overflow-y: auto;">
                        <table class="table table-bordered table-hover" id="inspectorsTable">
                            <thead class="table-light sticky-top" style="background-color: white; z-index: 10;">
                                <tr>
                                    <th style="color: black !important; background-color: #f8f9fa !important;">اسم
                                        المستخدم</th>
                                    <th style="color: black !important; background-color: #f8f9fa !important;">البريد
                                        الإلكتروني</th>
                                    <th style="color: black !important; background-color: #f8f9fa !important;">عدد
                                        الأساتذة</th>
                                    <th style="color: black !important; background-color: #f8f9fa !important;">الإجراءات
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for inspector in inspectors[:20] %}
                                <tr class="inspector-row" data-username="{{ inspector.username|lower }}"
                                    data-email="{{ inspector.email|lower }}">
                                    <td>{{ inspector.username }}</td>
                                    <td>{{ inspector.email }}</td>
                                    <td>{{ inspector.supervised_teachers.count() }}</td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="#" class="btn btn-sm btn-primary" data-bs-toggle="modal"
                                                data-bs-target="#viewInspectorModal" data-id="{{ inspector.id }}"
                                                data-name="{{ inspector.username }}"><i class="fas fa-eye"></i></a>
                                            <a href="#" class="btn btn-sm btn-warning" data-bs-toggle="modal"
                                                data-bs-target="#editInspectorModal" data-id="{{ inspector.id }}"
                                                data-name="{{ inspector.username }}"
                                                data-email="{{ inspector.email }}"><i class="fas fa-edit"></i></a>
                                            {% if inspector.is_active %}
                                            <a href="{{ url_for('toggle_user_status', user_id=inspector.id) }}"
                                                class="btn btn-sm btn-secondary"
                                                onclick="return confirm('هل أنت متأكد من تعطيل هذا الحساب؟')"><i
                                                    class="fas fa-ban"></i></a>
                                            {% else %}
                                            <a href="{{ url_for('toggle_user_status', user_id=inspector.id) }}"
                                                class="btn btn-sm btn-success"
                                                onclick="return confirm('هل أنت متأكد من تفعيل هذا الحساب؟')"><i
                                                    class="fas fa-check"></i></a>
                                            {% endif %}
                                            <a href="{{ url_for('delete_user', user_id=inspector.id) }}"
                                                class="btn btn-sm btn-danger"
                                                onclick="return confirm('هل أنت متأكد من حذف هذا الحساب؟ لا يمكن التراجع عن هذه العملية.')"><i
                                                    class="fas fa-trash"></i></a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                                {% if inspectors|length > 20 %}
                                <tr>
                                    <td colspan="4" class="text-center text-muted">
                                        <small><i class="fas fa-info-circle me-1"></i>يتم عرض أول 20 مفتش من أصل {{
                                            inspectors|length }}. استخدم البحث للعثور على مفتش محدد.</small>
                                    </td>
                                </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header" data-bs-toggle="collapse" data-bs-target="#teachersCollapse" aria-expanded="false"
                aria-controls="teachersCollapse" style="cursor: pointer;">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-chalkboard-teacher me-1"></i>
                        الأساتذة ({{ teachers|length }})
                    </div>
                    <i class="fas fa-chevron-down collapse-icon"></i>
                </div>
            </div>
            <div class="collapse" id="teachersCollapse">
                <div class="card-body">
                    <!-- أداة البحث -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" class="form-control" id="teacherSearch"
                                    placeholder="ابحث عن أستاذ (الاسم أو البريد الإلكتروني أو المشرف)..."
                                    onkeyup="filterTeachers()">
                                <button class="btn btn-outline-secondary" type="button" onclick="clearTeacherSearch()">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <small class="text-muted">
                                <span id="teacherCount">{{ teachers|length }}</span> أستاذ
                                <span id="teacherFilteredCount" style="display: none;">
                                    | <span id="teacherFilteredNumber">0</span> مطابق للبحث
                                </span>
                            </small>
                        </div>
                    </div>

                    <div class="table-responsive" style="max-height: 500px; overflow-y: auto;">
                        <table class="table table-bordered table-hover" id="teachersTable">
                            <thead class="table-light sticky-top" style="background-color: white; z-index: 10;">
                                <tr>
                                    <th style="color: black !important; background-color: #f8f9fa !important;">اسم
                                        المستخدم</th>
                                    <th style="color: black !important; background-color: #f8f9fa !important;">البريد
                                        الإلكتروني</th>
                                    <th style="color: black !important; background-color: #f8f9fa !important;">المشرف
                                    </th>
                                    <th style="color: black !important; background-color: #f8f9fa !important;">الإجراءات
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for teacher in teachers[:20] %}
                                <tr class="teacher-row" data-username="{{ teacher.username|lower }}"
                                    data-email="{{ teacher.email|lower }}"
                                    data-supervisor="{% if teacher.inspectors.first() %}{{ teacher.inspectors.first().username|lower }}{% else %}غير محدد{% endif %}">
                                    <td>{{ teacher.username }}</td>
                                    <td>{{ teacher.email }}</td>
                                    <td>
                                        {% if teacher.inspectors.first() %}
                                        {{ teacher.inspectors.first().username }}
                                        {% else %}
                                        غير محدد
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="#" class="btn btn-sm btn-primary" data-bs-toggle="modal"
                                                data-bs-target="#viewTeacherModal" data-id="{{ teacher.id }}"
                                                data-name="{{ teacher.username }}"><i class="fas fa-eye"></i></a>
                                            <a href="#" class="btn btn-sm btn-warning" data-bs-toggle="modal"
                                                data-bs-target="#editTeacherModal" data-id="{{ teacher.id }}"
                                                data-name="{{ teacher.username }}" data-email="{{ teacher.email }}"><i
                                                    class="fas fa-edit"></i></a>
                                            {% if teacher.is_active %}
                                            <a href="{{ url_for('toggle_user_status', user_id=teacher.id) }}"
                                                class="btn btn-sm btn-secondary"
                                                onclick="return confirm('هل أنت متأكد من تعطيل هذا الحساب؟')"><i
                                                    class="fas fa-ban"></i></a>
                                            {% else %}
                                            <a href="{{ url_for('toggle_user_status', user_id=teacher.id) }}"
                                                class="btn btn-sm btn-success"
                                                onclick="return confirm('هل أنت متأكد من تفعيل هذا الحساب؟')"><i
                                                    class="fas fa-check"></i></a>
                                            {% endif %}
                                            <a href="{{ url_for('delete_user', user_id=teacher.id) }}"
                                                class="btn btn-sm btn-danger"
                                                onclick="return confirm('هل أنت متأكد من حذف هذا الحساب؟ لا يمكن التراجع عن هذه العملية.')"><i
                                                    class="fas fa-trash"></i></a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                                {% if teachers|length > 20 %}
                                <tr>
                                    <td colspan="4" class="text-center text-muted">
                                        <small><i class="fas fa-info-circle me-1"></i>يتم عرض أول 20 أستاذ من أصل {{
                                            teachers|length }}. استخدم البحث للعثور على أستاذ محدد.</small>
                                    </td>
                                </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- مودال المفتشين -->
<div class="modal fade" id="inspectorsModal" tabindex="-1" aria-labelledby="inspectorsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="inspectorsModalLabel">تفاصيل المفتشين</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                    aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="table-light">
                            <tr>
                                <th style="color: black !important; background-color: #f8f9fa !important;">اسم المستخدم
                                </th>
                                <th style="color: black !important; background-color: #f8f9fa !important;">البريد
                                    الإلكتروني</th>
                                <th style="color: black !important; background-color: #f8f9fa !important;">عدد الأساتذة
                                </th>
                                <th style="color: black !important; background-color: #f8f9fa !important;">الإجراءات
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for inspector in inspectors %}
                            <tr>
                                <td>{{ inspector.username }}</td>
                                <td>{{ inspector.email }}</td>
                                <td>{{ inspector.supervised_teachers.count() }}</td>
                                <td>
                                    <a href="{{ url_for('dashboard') }}" class="btn btn-sm btn-primary"><i
                                            class="fas fa-eye"></i> عرض</a>
                                    <a href="#" class="btn btn-sm btn-warning"><i class="fas fa-edit"></i> تعديل</a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- مودال المستويات التعليمية -->
<div class="modal fade" id="levelsModal" tabindex="-1" aria-labelledby="levelsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="levelsModalLabel">تفاصيل المستويات التعليمية</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                    aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="table-light">
                            <tr>
                                <th style="color: black !important; background-color: #f8f9fa !important;">المستوى</th>
                                <th style="color: black !important; background-color: #f8f9fa !important;">عدد المواد
                                </th>
                                <th style="color: black !important; background-color: #f8f9fa !important;">عدد الميادين
                                </th>
                                <th style="color: black !important; background-color: #f8f9fa !important;">عدد الكفاءات
                                </th>
                                <th style="color: black !important; background-color: #f8f9fa !important;">الإجراءات
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>السنة الأولى ابتدائي</td>
                                <td>13</td>
                                <td>25</td>
                                <td>50</td>
                                <td>
                                    <a href="{{ url_for('manage_databases') }}" class="btn btn-sm btn-primary"><i
                                            class="fas fa-eye"></i> عرض</a>
                                </td>
                            </tr>
                            <tr>
                                <td>السنة الثانية ابتدائي</td>
                                <td>13</td>
                                <td>25</td>
                                <td>50</td>
                                <td>
                                    <a href="{{ url_for('manage_databases') }}" class="btn btn-sm btn-primary"><i
                                            class="fas fa-eye"></i> عرض</a>
                                </td>
                            </tr>
                            <tr>
                                <td>السنة الثالثة ابتدائي</td>
                                <td>13</td>
                                <td>25</td>
                                <td>50</td>
                                <td>
                                    <a href="{{ url_for('manage_databases') }}" class="btn btn-sm btn-primary"><i
                                            class="fas fa-eye"></i> عرض</a>
                                </td>
                            </tr>
                            <tr>
                                <td>السنة الرابعة ابتدائي</td>
                                <td>13</td>
                                <td>25</td>
                                <td>50</td>
                                <td>
                                    <a href="{{ url_for('manage_databases') }}" class="btn btn-sm btn-primary"><i
                                            class="fas fa-eye"></i> عرض</a>
                                </td>
                            </tr>
                            <tr>
                                <td>السنة الخامسة ابتدائي</td>
                                <td>13</td>
                                <td>25</td>
                                <td>50</td>
                                <td>
                                    <a href="{{ url_for('manage_databases') }}" class="btn btn-sm btn-primary"><i
                                            class="fas fa-eye"></i> عرض</a>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- مودال المواد الدراسية -->
<div class="modal fade" id="subjectsModal" tabindex="-1" aria-labelledby="subjectsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-warning text-white">
                <h5 class="modal-title" id="subjectsModalLabel">تفاصيل المواد الدراسية</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                    aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- مفتاح الألوان -->
                <div class="row mb-3">
                    <div class="col-12">
                        <div class="card bg-light">
                            <div class="card-body py-2">
                                <h6 class="card-title mb-2">
                                    <i class="fas fa-palette me-1"></i>
                                    مفتاح الألوان حسب المستوى التعليمي
                                </h6>
                                <div class="d-flex flex-wrap gap-3">
                                    <div class="d-flex align-items-center">
                                        <span class="badge bg-primary me-2" style="width: 20px; height: 20px;"></span>
                                        <small>السنة الأولى ابتدائي</small>
                                    </div>
                                    <div class="d-flex align-items-center">
                                        <span class="badge bg-success me-2" style="width: 20px; height: 20px;"></span>
                                        <small>السنة الثانية ابتدائي</small>
                                    </div>
                                    <div class="d-flex align-items-center">
                                        <span class="badge bg-warning me-2" style="width: 20px; height: 20px;"></span>
                                        <small>السنة الثالثة ابتدائي</small>
                                    </div>
                                    <div class="d-flex align-items-center">
                                        <span class="badge bg-danger me-2" style="width: 20px; height: 20px;"></span>
                                        <small>السنة الرابعة ابتدائي</small>
                                    </div>
                                    <div class="d-flex align-items-center">
                                        <span class="badge bg-info me-2" style="width: 20px; height: 20px;"></span>
                                        <small>السنة الخامسة ابتدائي</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th style="color: white !important;">المادة</th>
                                <th style="color: white !important;">المستوى</th>
                                <th style="color: white !important;">عدد المواد المعرفية</th>
                                <th style="color: white !important;">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for level_name, level_data in subjects_data.items() %}
                            {% set level_color = level_data.level_color %}
                            {% set outer_loop = loop %}

                            <!-- إضافة صف فاصل للمستوى (إلا للمستوى الأول) -->
                            {% if not loop.first %}
                            <tr class="level-separator">
                                <td colspan="4"
                                    style="height: 6px; background: {{ level_color.hex }}; border: none; padding: 0; opacity: 0.3;">
                                </td>
                            </tr>
                            {% endif %}

                            {% for subject in level_data.subjects %}
                            <tr class="level-{{ level_color.color }}" {% if outer_loop.first and loop.first
                                %}style="border-top: 3px solid {{ level_color.hex }};" {% endif %}>
                                <td>
                                    <i class="fas fa-book me-1" style="color: {{ level_color.hex }};"></i>
                                    <span style="color: {{ level_color.hex }}; font-weight: 600;">{{ subject.name
                                        }}</span>
                                </td>
                                <td>
                                    <i class="fas fa-school me-1" style="color: {{ level_color.hex }};"></i>
                                    <span style="color: {{ level_color.hex }}; font-weight: 700; font-size: 1.05em;">{{
                                        level_name }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-{{ level_color.color }}" style="font-size: 0.9em;">{{
                                        subject.materials_count }}</span>
                                </td>
                                <td>
                                    <a href="{{ url_for('manage_databases') }}"
                                        class="btn btn-sm btn-outline-{{ level_color.color }}">
                                        <i class="fas fa-eye"></i> عرض
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                            {% endfor %}
                            {% if not subjects_data %}
                            <tr>
                                <td colspan="4" class="text-center">
                                    <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                                    لا توجد مواد دراسية متاحة
                                </td>
                            </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>

                <!-- إضافة CSS للتحسينات البصرية -->
                <style>
                    /* تحسين مظهر الجدول */
                    #subjectsModal .table tbody tr:hover {
                        background-color: rgba(0, 0, 0, 0.05);
                        transform: scale(1.01);
                        transition: all 0.2s ease;
                    }

                    /* تأثيرات على الأيقونات */
                    #subjectsModal .fas {
                        transition: all 0.3s ease;
                    }

                    #subjectsModal tr:hover .fas {
                        transform: scale(1.1);
                    }

                    /* تحسين مظهر الشارات */
                    #subjectsModal .badge {
                        font-size: 0.9em !important;
                        padding: 0.5em 0.8em;
                        border-radius: 0.5rem;
                    }

                    /* تحسين مظهر الأزرار */
                    #subjectsModal .btn {
                        transition: all 0.3s ease;
                        border-width: 2px;
                    }

                    #subjectsModal .btn:hover {
                        transform: translateY(-2px);
                        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
                    }

                    /* تحسين مظهر النصوص */
                    #subjectsModal td span {
                        transition: all 0.3s ease;
                    }

                    #subjectsModal tr:hover td span {
                        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
                    }

                    /* تحسين مظهر الفواصل بين المستويات */
                    #subjectsModal .level-separator td {
                        border: none !important;
                        padding: 0 !important;
                        height: 6px !important;
                    }

                    /* تحسين مظهر الصفوف حسب المستوى */
                    #subjectsModal .level-primary:hover {
                        background-color: rgba(13, 110, 253, 0.1) !important;
                    }

                    #subjectsModal .level-success:hover {
                        background-color: rgba(25, 135, 84, 0.1) !important;
                    }

                    #subjectsModal .level-warning:hover {
                        background-color: rgba(253, 126, 20, 0.1) !important;
                    }

                    #subjectsModal .level-danger:hover {
                        background-color: rgba(220, 53, 69, 0.1) !important;
                    }

                    #subjectsModal .level-info:hover {
                        background-color: rgba(13, 202, 240, 0.1) !important;
                    }

                    /* تحسين مظهر العناوين */
                    #subjectsModal thead th {
                        background-color: #212529 !important;
                        color: white !important;
                        font-weight: 700;
                        text-align: center;
                        border-color: #495057 !important;
                    }
                </style>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- مودال الكفاءات المستهدفة -->
<div class="modal fade" id="competenciesModal" tabindex="-1" aria-labelledby="competenciesModalLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="competenciesModalLabel">تفاصيل الكفاءات المستهدفة</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                    aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> يمكنك عرض الكفاءات المستهدفة من خلال صفحة قواعد البيانات.
                </div>
                <div class="text-center mt-3">
                    <a href="{{ url_for('manage_databases') }}" class="btn btn-primary btn-lg">
                        <i class="fas fa-database"></i> الذهاب إلى قواعد البيانات
                    </a>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- مودال عرض المفتش -->
<div class="modal fade" id="viewInspectorModal" tabindex="-1" aria-labelledby="viewInspectorModalLabel"
    aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="viewInspectorModalLabel">تفاصيل المفتش</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                    aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label fw-bold">اسم المستخدم:</label>
                    <p id="viewInspectorUsername"></p>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-bold">البريد الإلكتروني:</label>
                    <p id="viewInspectorEmail"></p>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-bold">عدد الأساتذة تحت الإشراف:</label>
                    <p id="viewInspectorTeachersCount"></p>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-bold">الحالة:</label>
                    <p id="viewInspectorStatus"></p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- مودال تعديل المفتش -->
<div class="modal fade" id="editInspectorModal" tabindex="-1" aria-labelledby="editInspectorModalLabel"
    aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-warning text-white">
                <h5 class="modal-title" id="editInspectorModalLabel">تعديل بيانات المفتش</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                    aria-label="Close"></button>
            </div>
            <form id="editInspectorForm" action="{{ url_for('edit_user') }}" method="post">
                <div class="modal-body">
                    <input type="hidden" id="editInspectorId" name="user_id">
                    <div class="mb-3">
                        <label for="editInspectorUsername" class="form-label">اسم المستخدم</label>
                        <input type="text" class="form-control" id="editInspectorUsername" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label for="editInspectorEmail" class="form-label">البريد الإلكتروني</label>
                        <input type="email" class="form-control" id="editInspectorEmail" name="email" required>
                    </div>
                    <div class="mb-3">
                        <label for="editInspectorPassword" class="form-label">كلمة المرور (اتركها فارغة للاحتفاظ بنفس
                            كلمة المرور)</label>
                        <input type="password" class="form-control" id="editInspectorPassword" name="password">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-warning">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- مودال عرض الأستاذ -->
<div class="modal fade" id="viewTeacherModal" tabindex="-1" aria-labelledby="viewTeacherModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="viewTeacherModalLabel">تفاصيل الأستاذ</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                    aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label fw-bold">اسم المستخدم:</label>
                    <p id="viewTeacherUsername"></p>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-bold">البريد الإلكتروني:</label>
                    <p id="viewTeacherEmail"></p>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-bold">المشرف:</label>
                    <p id="viewTeacherInspector"></p>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-bold">الحالة:</label>
                    <p id="viewTeacherStatus"></p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- مودال تعديل الأستاذ -->
<div class="modal fade" id="editTeacherModal" tabindex="-1" aria-labelledby="editTeacherModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-warning text-white">
                <h5 class="modal-title" id="editTeacherModalLabel">تعديل بيانات الأستاذ</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                    aria-label="Close"></button>
            </div>
            <form id="editTeacherForm" action="{{ url_for('edit_user') }}" method="post">
                <div class="modal-body">
                    <input type="hidden" id="editTeacherId" name="user_id">
                    <div class="mb-3">
                        <label for="editTeacherUsername" class="form-label">اسم المستخدم</label>
                        <input type="text" class="form-control" id="editTeacherUsername" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label for="editTeacherEmail" class="form-label">البريد الإلكتروني</label>
                        <input type="email" class="form-control" id="editTeacherEmail" name="email" required>
                    </div>
                    <div class="mb-3">
                        <label for="editTeacherPassword" class="form-label">كلمة المرور (اتركها فارغة للاحتفاظ بنفس كلمة
                            المرور)</label>
                        <input type="password" class="form-control" id="editTeacherPassword" name="password">
                    </div>
                    <div class="mb-3">
                        <label for="editTeacherInspector" class="form-label">المشرف</label>
                        <select class="form-select" id="editTeacherInspector" name="inspector_id">
                            <option value="">اختر المشرف</option>
                            {% for inspector in inspectors %}
                            <option value="{{ inspector.id }}">{{ inspector.username }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-warning">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
    // كود JavaScript للتعامل مع المودالات
    document.addEventListener('DOMContentLoaded', function () {
        // التعامل مع مودال المفتشين
        const inspectorsModal = document.getElementById('inspectorsModal');
        if (inspectorsModal) {
            inspectorsModal.addEventListener('show.bs.modal', function (event) {
                // يمكن إضافة كود لتحديث البيانات في المودال قبل عرضه
            });
        }

        // التعامل مع مودال المستويات
        const levelsModal = document.getElementById('levelsModal');
        if (levelsModal) {
            levelsModal.addEventListener('show.bs.modal', function (event) {
                // يمكن إضافة كود لتحديث البيانات في المودال قبل عرضه
            });
        }

        // التعامل مع مودال المواد
        const subjectsModal = document.getElementById('subjectsModal');
        if (subjectsModal) {
            subjectsModal.addEventListener('show.bs.modal', function (event) {
                // يمكن إضافة كود لتحديث البيانات في المودال قبل عرضه
            });
        }

        // التعامل مع مودال الكفاءات
        const competenciesModal = document.getElementById('competenciesModal');
        if (competenciesModal) {
            competenciesModal.addEventListener('show.bs.modal', function (event) {
                // يمكن إضافة كود لتحديث البيانات في المودال قبل عرضه
            });
        }

        // التعامل مع مودال عرض المفتش
        const viewInspectorModal = document.getElementById('viewInspectorModal');
        if (viewInspectorModal) {
            viewInspectorModal.addEventListener('show.bs.modal', function (event) {
                const button = event.relatedTarget;
                const id = button.getAttribute('data-id');
                const name = button.getAttribute('data-name');
                const email = button.closest('tr').querySelector('td:nth-child(2)').textContent;
                const teachersCount = button.closest('tr').querySelector('td:nth-child(3)').textContent;
                const isActive = button.closest('tr').querySelector('.btn-secondary') ? true : false;

                document.getElementById('viewInspectorUsername').textContent = name;
                document.getElementById('viewInspectorEmail').textContent = email;
                document.getElementById('viewInspectorTeachersCount').textContent = teachersCount;
                document.getElementById('viewInspectorStatus').textContent = isActive ? 'مفعل' : 'معطل';
                document.getElementById('viewInspectorStatus').className = isActive ? 'badge bg-success' : 'badge bg-secondary';
            });
        }

        // التعامل مع مودال تعديل المفتش
        const editInspectorModal = document.getElementById('editInspectorModal');
        if (editInspectorModal) {
            editInspectorModal.addEventListener('show.bs.modal', function (event) {
                const button = event.relatedTarget;
                const id = button.getAttribute('data-id');
                const name = button.getAttribute('data-name');
                const email = button.getAttribute('data-email');

                document.getElementById('editInspectorId').value = id;
                document.getElementById('editInspectorUsername').value = name;
                document.getElementById('editInspectorEmail').value = email;
                document.getElementById('editInspectorPassword').value = '';
            });
        }

        // التعامل مع مودال عرض الأستاذ
        const viewTeacherModal = document.getElementById('viewTeacherModal');
        if (viewTeacherModal) {
            viewTeacherModal.addEventListener('show.bs.modal', function (event) {
                const button = event.relatedTarget;
                const id = button.getAttribute('data-id');
                const name = button.getAttribute('data-name');
                const email = button.closest('tr').querySelector('td:nth-child(2)').textContent;
                const inspector = button.closest('tr').querySelector('td:nth-child(3)').textContent;
                const isActive = button.closest('tr').querySelector('.btn-secondary') ? true : false;

                document.getElementById('viewTeacherUsername').textContent = name;
                document.getElementById('viewTeacherEmail').textContent = email;
                document.getElementById('viewTeacherInspector').textContent = inspector;
                document.getElementById('viewTeacherStatus').textContent = isActive ? 'مفعل' : 'معطل';
                document.getElementById('viewTeacherStatus').className = isActive ? 'badge bg-success' : 'badge bg-secondary';
            });
        }

        // التعامل مع مودال تعديل الأستاذ
        const editTeacherModal = document.getElementById('editTeacherModal');
        if (editTeacherModal) {
            editTeacherModal.addEventListener('show.bs.modal', function (event) {
                const button = event.relatedTarget;
                const id = button.getAttribute('data-id');
                const name = button.getAttribute('data-name');
                const email = button.getAttribute('data-email');
                const inspectorRow = button.closest('tr').querySelector('td:nth-child(3)').textContent.trim();

                document.getElementById('editTeacherId').value = id;
                document.getElementById('editTeacherUsername').value = name;
                document.getElementById('editTeacherEmail').value = email;
                document.getElementById('editTeacherPassword').value = '';

                // محاولة تحديد المشرف الحالي في القائمة
                const inspectorSelect = document.getElementById('editTeacherInspector');
                if (inspectorSelect) {
                    const options = inspectorSelect.options;
                    for (let i = 0; i < options.length; i++) {
                        if (options[i].text === inspectorRow) {
                            inspectorSelect.selectedIndex = i;
                            break;
                        }
                    }
                }
            });
        }
    });

    // تحديث أيقونات الطي
    const collapseElements = document.querySelectorAll('.collapse');
    collapseElements.forEach(function (element) {
        element.addEventListener('show.bs.collapse', function () {
            const icon = document.querySelector(`[data-bs-target="#${element.id}"] .collapse-icon`);
            if (icon) {
                icon.classList.remove('fa-chevron-down');
                icon.classList.add('fa-chevron-up');
            }
        });

        element.addEventListener('hide.bs.collapse', function () {
            const icon = document.querySelector(`[data-bs-target="#${element.id}"] .collapse-icon`);
            if (icon) {
                icon.classList.remove('fa-chevron-up');
                icon.classList.add('fa-chevron-down');
            }
        });
    });

    // إضافة مستمعات للضغط على Enter في خانات البحث
    const inspectorSearch = document.getElementById('inspectorSearch');
    const teacherSearch = document.getElementById('teacherSearch');

    if (inspectorSearch) {
        inspectorSearch.addEventListener('keydown', function (event) {
            if (event.key === 'Enter') {
                event.preventDefault();
            }
        });
    }

    if (teacherSearch) {
        teacherSearch.addEventListener('keydown', function (event) {
            if (event.key === 'Enter') {
                event.preventDefault();
            }
        });
    }

    // دوال البحث للمفتشين
    window.filterInspectors = function () {
        const searchTerm = document.getElementById('inspectorSearch').value.toLowerCase();
        const rows = document.querySelectorAll('.inspector-row');
        let visibleCount = 0;

        rows.forEach(function (row) {
            const username = row.getAttribute('data-username');
            const email = row.getAttribute('data-email');

            if (username.includes(searchTerm) || email.includes(searchTerm)) {
                row.style.display = '';
                visibleCount++;
            } else {
                row.style.display = 'none';
            }
        });

        updateInspectorCount(visibleCount, searchTerm !== '');
    };

    window.clearInspectorSearch = function () {
        document.getElementById('inspectorSearch').value = '';
        const rows = document.querySelectorAll('.inspector-row');

        rows.forEach(function (row) {
            row.style.display = '';
        });

        updateInspectorCount(0, false);
        document.getElementById('inspectorSearch').focus();
    };

    function updateInspectorCount(filteredCount, isFiltering) {
        const filteredCountElement = document.getElementById('inspectorFilteredCount');
        const filteredNumberElement = document.getElementById('inspectorFilteredNumber');

        if (isFiltering && filteredCountElement && filteredNumberElement) {
            filteredNumberElement.textContent = filteredCount;
            filteredCountElement.style.display = '';
        } else if (filteredCountElement) {
            filteredCountElement.style.display = 'none';
        }
    }

    // دوال البحث للأساتذة
    window.filterTeachers = function () {
        const searchTerm = document.getElementById('teacherSearch').value.toLowerCase();
        const rows = document.querySelectorAll('.teacher-row');
        let visibleCount = 0;

        rows.forEach(function (row) {
            const username = row.getAttribute('data-username');
            const email = row.getAttribute('data-email');
            const supervisor = row.getAttribute('data-supervisor');

            if (username.includes(searchTerm) || email.includes(searchTerm) || supervisor.includes(searchTerm)) {
                row.style.display = '';
                visibleCount++;
            } else {
                row.style.display = 'none';
            }
        });

        updateTeacherCount(visibleCount, searchTerm !== '');
    };

    window.clearTeacherSearch = function () {
        document.getElementById('teacherSearch').value = '';
        const rows = document.querySelectorAll('.teacher-row');

        rows.forEach(function (row) {
            row.style.display = '';
        });

        updateTeacherCount(0, false);
        document.getElementById('teacherSearch').focus();
    };

    function updateTeacherCount(filteredCount, isFiltering) {
        const filteredCountElement = document.getElementById('teacherFilteredCount');
        const filteredNumberElement = document.getElementById('teacherFilteredNumber');

        if (isFiltering && filteredCountElement && filteredNumberElement) {
            filteredNumberElement.textContent = filteredCount;
            filteredCountElement.style.display = '';
        } else if (filteredCountElement) {
            filteredCountElement.style.display = 'none';
        }
    }
</script>

<style>
    /* تحسين مظهر الحاويات القابلة للطي */
    .card-header[data-bs-toggle="collapse"] {
        transition: all 0.3s ease;
    }

    .card-header[data-bs-toggle="collapse"]:hover {
        background-color: #f8f9fa;
    }

    .collapse-icon {
        transition: transform 0.3s ease;
    }

    /* تحسين مظهر أدوات البحث */
    .input-group .form-control:focus {
        border-color: #0d6efd;
        box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    }

    .btn-outline-secondary {
        border-color: #6c757d;
    }

    .btn-outline-secondary:hover {
        background-color: #6c757d;
        border-color: #6c757d;
        color: white;
    }

    /* تحسين مظهر الجداول */
    .table-responsive {
        border-radius: 0.375rem;
    }

    .table th {
        border-top: none;
        font-weight: 600;
    }

    .table td {
        vertical-align: middle;
    }

    /* تحسين مظهر الأزرار */
    .btn-group-sm .btn {
        margin: 0 1px;
    }

    /* تحسين مظهر العدادات */
    small.text-muted {
        font-size: 0.875em;
        line-height: 1.5;
    }

    /* تأثيرات الحركة */
    .inspector-row,
    .teacher-row {
        transition: opacity 0.3s ease;
    }

    .inspector-row[style*="display: none"],
    .teacher-row[style*="display: none"] {
        opacity: 0;
    }
</style>
{% endblock %}

<!-- مودال إنشاء مستخدم جديد -->
<div class="modal fade" id="createUserModal" tabindex="-1" aria-labelledby="createUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="createUserModalLabel">
                    <i class="fas fa-user-plus animated-icon me-2"></i>
                    إنشاء حساب جديد
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                    aria-label="Close"></button>
            </div>
            <form action="{{ url_for('admin_create_user') }}" method="post">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="createUsername" name="username" required>
                                <label for="createUsername">
                                    <i class="fas fa-user me-1"></i>
                                    اسم المستخدم
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="email" class="form-control" id="createEmail" name="email" required>
                                <label for="createEmail">
                                    <i class="fas fa-envelope me-1"></i>
                                    البريد الإلكتروني
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="password" class="form-control" id="createPassword" name="password"
                                    required>
                                <label for="createPassword">
                                    <i class="fas fa-lock me-1"></i>
                                    كلمة المرور
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="tel" class="form-control" id="createPhone" name="phone_number"
                                    pattern="^(0[5-7]\d{8}|0[2-4]\d{7}|\+213[5-7]\d{8}|\+213[2-4]\d{7})$"
                                    title="أدخل رقم هاتف جزائري صحيح" required>
                                <label for="createPhone">
                                    <i class="fas fa-phone me-1"></i>
                                    رقم الهاتف
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <select class="form-select" id="createWilaya" name="wilaya_code">
                                    <option value="">اختر الولاية (اختياري)</option>
                                    <option value="01">01 - أدرار</option>
                                    <option value="02">02 - الشلف</option>
                                    <option value="03">03 - الأغواط</option>
                                    <option value="04">04 - أم البواقي</option>
                                    <option value="05">05 - باتنة</option>
                                    <option value="06">06 - بجاية</option>
                                    <option value="07">07 - بسكرة</option>
                                    <option value="08">08 - بشار</option>
                                    <option value="09">09 - البليدة</option>
                                    <option value="10">10 - البويرة</option>
                                    <option value="11">11 - تمنراست</option>
                                    <option value="12">12 - تبسة</option>
                                    <option value="13">13 - تلمسان</option>
                                    <option value="14">14 - تيارت</option>
                                    <option value="15">15 - تيزي وزو</option>
                                    <option value="16">16 - الجزائر</option>
                                    <option value="17">17 - الجلفة</option>
                                    <option value="18">18 - جيجل</option>
                                    <option value="19">19 - سطيف</option>
                                    <option value="20">20 - سعيدة</option>
                                    <option value="21">21 - سكيكدة</option>
                                    <option value="22">22 - سيدي بلعباس</option>
                                    <option value="23">23 - عنابة</option>
                                    <option value="24">24 - قالمة</option>
                                    <option value="25">25 - قسنطينة</option>
                                    <option value="26">26 - المدية</option>
                                    <option value="27">27 - مستغانم</option>
                                    <option value="28">28 - المسيلة</option>
                                    <option value="29">29 - معسكر</option>
                                    <option value="30">30 - ورقلة</option>
                                    <option value="31">31 - وهران</option>
                                    <option value="32">32 - البيض</option>
                                    <option value="33">33 - إليزي</option>
                                    <option value="34">34 - برج بوعريريج</option>
                                    <option value="35">35 - بومرداس</option>
                                    <option value="36">36 - الطارف</option>
                                    <option value="37">37 - تندوف</option>
                                    <option value="38">38 - تيسمسيلت</option>
                                    <option value="39">39 - الوادي</option>
                                    <option value="40">40 - خنشلة</option>
                                    <option value="41">41 - سوق أهراس</option>
                                    <option value="42">42 - تيبازة</option>
                                    <option value="43">43 - ميلة</option>
                                    <option value="44">44 - عين الدفلى</option>
                                    <option value="45">45 - النعامة</option>
                                    <option value="46">46 - عين تموشنت</option>
                                    <option value="47">47 - غرداية</option>
                                    <option value="48">48 - غليزان</option>
                                    <option value="49">49 - تيميمون</option>
                                    <option value="50">50 - برج باجي مختار</option>
                                    <option value="51">51 - أولاد جلال</option>
                                    <option value="52">52 - بني عباس</option>
                                    <option value="53">53 - عين صالح</option>
                                    <option value="54">54 - عين قزام</option>
                                    <option value="55">55 - تقرت</option>
                                    <option value="56">56 - جانت</option>
                                    <option value="57">57 - المغير</option>
                                    <option value="58">58 - المنيعة</option>
                                </select>
                                <label for="createWilaya">
                                    <i class="fas fa-map-marker-alt me-1"></i>
                                    الولاية
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <select class="form-select" id="createRole" name="role" required>
                                    <option value="" selected disabled>اختر الدور</option>
                                    <option value="teacher">أستاذ</option>
                                    <option value="inspector">مفتش</option>
                                    <option value="user_manager">مدير المستخدمين</option>
                                    <option value="admin">إدارة</option>
                                </select>
                                <label for="createRole">
                                    <i class="fas fa-user-tag me-1"></i>
                                    الدور
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>ملاحظة:</strong> سيتم تفعيل الحساب تلقائياً عند الإنشاء من قبل الإدارة.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>
                        إلغاء
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i>
                        إنشاء الحساب
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- مودال عرض حسابات الإدارة -->
<div class="modal fade" id="adminsModal" tabindex="-1" aria-labelledby="adminsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-secondary text-white">
                <h5 class="modal-title" id="adminsModalLabel">
                    <i class="fas fa-user-shield animated-icon me-2"></i>
                    حسابات الإدارة
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                    aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead class="table-light">
                            <tr>
                                <th style="color: black !important; background-color: #f8f9fa !important;">اسم المستخدم
                                </th>
                                <th style="color: black !important; background-color: #f8f9fa !important;">البريد
                                    الإلكتروني</th>
                                <th style="color: black !important; background-color: #f8f9fa !important;">تاريخ التسجيل
                                </th>
                                <th style="color: black !important; background-color: #f8f9fa !important;">الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for admin in admins %}
                            <tr>
                                <td>{{ admin.username }}</td>
                                <td>{{ admin.email }}</td>
                                <td>{{ admin.created_at.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    {% if admin.is_active %}
                                    <span class="badge bg-success">نشط</span>
                                    {% else %}
                                    <span class="badge bg-danger">معطل</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- مودال عرض الحسابات المعطلة -->
<div class="modal fade" id="pendingUsersModal" tabindex="-1" aria-labelledby="pendingUsersModalLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title" id="pendingUsersModalLabel">
                    <i class="fas fa-clock animated-icon me-2"></i>
                    الحسابات المعطلة (في انتظار التفعيل)
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                {% if pending_users %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead class="table-light">
                            <tr>
                                <th style="color: black !important; background-color: #f8f9fa !important;">اسم المستخدم
                                </th>
                                <th style="color: black !important; background-color: #f8f9fa !important;">البريد
                                    الإلكتروني</th>
                                <th style="color: black !important; background-color: #f8f9fa !important;">الدور</th>
                                <th style="color: black !important; background-color: #f8f9fa !important;">تاريخ التسجيل
                                </th>
                                <th style="color: black !important; background-color: #f8f9fa !important;">الإجراءات
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for user in pending_users %}
                            <tr>
                                <td>{{ user.username }}</td>
                                <td>{{ user.email }}</td>
                                <td>
                                    {% if user.role == 'admin' %}
                                    <span class="badge bg-danger">إدارة</span>
                                    {% elif user.role == 'inspector' %}
                                    <span class="badge bg-warning">مفتش</span>
                                    {% elif user.role == 'user_manager' %}
                                    <span class="badge bg-info">مدير المستخدمين</span>
                                    {% else %}
                                    <span class="badge bg-success">أستاذ</span>
                                    {% endif %}
                                </td>
                                <td>{{ user.created_at.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    <a href="{{ url_for('toggle_user_status', user_id=user.id) }}"
                                        class="btn btn-sm btn-success"
                                        onclick="return confirm('هل أنت متأكد من تفعيل هذا الحساب؟')">
                                        <i class="fas fa-check"></i> تفعيل
                                    </a>
                                    <a href="{{ url_for('delete_user', user_id=user.id) }}"
                                        class="btn btn-sm btn-danger"
                                        onclick="return confirm('هل أنت متأكد من حذف هذا الحساب؟')">
                                        <i class="fas fa-trash"></i> حذف
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                    <h5>لا توجد حسابات معطلة</h5>
                    <p class="text-muted">جميع الحسابات مفعلة حالياً</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- مودال إنشاء مدير مستخدمين جديد -->
<div class="modal fade" id="createUserManagerModal" tabindex="-1" aria-labelledby="createUserManagerModalLabel"
    aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="createUserManagerModalLabel">
                    <i class="fas fa-user-plus animated-icon me-2"></i>
                    إنشاء مدير مستخدمين جديد
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                    aria-label="Close"></button>
            </div>
            <form action="{{ url_for('admin_create_user_manager') }}" method="post">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="userManagerUsername" name="username"
                                    required>
                                <label for="userManagerUsername">
                                    <i class="fas fa-user me-1"></i>
                                    اسم المستخدم
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="email" class="form-control" id="userManagerEmail" name="email" required>
                                <label for="userManagerEmail">
                                    <i class="fas fa-envelope me-1"></i>
                                    البريد الإلكتروني
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="password" class="form-control" id="userManagerPassword" name="password"
                                    required>
                                <label for="userManagerPassword">
                                    <i class="fas fa-lock me-1"></i>
                                    كلمة المرور
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="tel" class="form-control" id="userManagerPhone" name="phone_number"
                                    pattern="^(0[5-7]\d{8}|0[2-4]\d{7}|\+213[5-7]\d{8}|\+213[2-4]\d{7})$"
                                    title="أدخل رقم هاتف جزائري صحيح" required>
                                <label for="userManagerPhone">
                                    <i class="fas fa-phone me-1"></i>
                                    رقم الهاتف
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="form-floating mb-3">
                        <select class="form-select" id="userManagerWilaya" name="wilaya_code">
                            <option value="">اختر الولاية (اختياري)</option>
                            <option value="01">01 - أدرار</option>
                            <option value="02">02 - الشلف</option>
                            <option value="03">03 - الأغواط</option>
                            <option value="04">04 - أم البواقي</option>
                            <option value="05">05 - باتنة</option>
                            <option value="06">06 - بجاية</option>
                            <option value="07">07 - بسكرة</option>
                            <option value="08">08 - بشار</option>
                            <option value="09">09 - البليدة</option>
                            <option value="10">10 - البويرة</option>
                            <option value="11">11 - تمنراست</option>
                            <option value="12">12 - تبسة</option>
                            <option value="13">13 - تلمسان</option>
                            <option value="14">14 - تيارت</option>
                            <option value="15">15 - تيزي وزو</option>
                            <option value="16">16 - الجزائر</option>
                            <option value="17">17 - الجلفة</option>
                            <option value="18">18 - جيجل</option>
                            <option value="19">19 - سطيف</option>
                            <option value="20">20 - سعيدة</option>
                            <option value="21">21 - سكيكدة</option>
                            <option value="22">22 - سيدي بلعباس</option>
                            <option value="23">23 - عنابة</option>
                            <option value="24">24 - قالمة</option>
                            <option value="25">25 - قسنطينة</option>
                            <option value="26">26 - المدية</option>
                            <option value="27">27 - مستغانم</option>
                            <option value="28">28 - المسيلة</option>
                            <option value="29">29 - معسكر</option>
                            <option value="30">30 - ورقلة</option>
                            <option value="31">31 - وهران</option>
                            <option value="32">32 - البيض</option>
                            <option value="33">33 - إليزي</option>
                            <option value="34">34 - برج بوعريريج</option>
                            <option value="35">35 - بومرداس</option>
                            <option value="36">36 - الطارف</option>
                            <option value="37">37 - تندوف</option>
                            <option value="38">38 - تيسمسيلت</option>
                            <option value="39">39 - الوادي</option>
                            <option value="40">40 - خنشلة</option>
                            <option value="41">41 - سوق أهراس</option>
                            <option value="42">42 - تيبازة</option>
                            <option value="43">43 - ميلة</option>
                            <option value="44">44 - عين الدفلى</option>
                            <option value="45">45 - النعامة</option>
                            <option value="46">46 - عين تموشنت</option>
                            <option value="47">47 - غرداية</option>
                            <option value="48">48 - غليزان</option>
                            <option value="49">49 - تيميمون</option>
                            <option value="50">50 - برج باجي مختار</option>
                            <option value="51">51 - أولاد جلال</option>
                            <option value="52">52 - بني عباس</option>
                            <option value="53">53 - عين صالح</option>
                            <option value="54">54 - عين قزام</option>
                            <option value="55">55 - تقرت</option>
                            <option value="56">56 - جانت</option>
                            <option value="57">57 - المغير</option>
                            <option value="58">58 - المنيعة</option>
                        </select>
                        <label for="userManagerWilaya">
                            <i class="fas fa-map-marker-alt me-1"></i>
                            الولاية
                        </label>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        سيتم إنشاء حساب بدور "مدير المستخدمين" مع صلاحيات محدودة لإدارة المستخدمين بشكل فردي فقط.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>
                        إلغاء
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-user-plus me-1"></i>
                        إنشاء الحساب
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- مودال عرض مديري المستخدمين -->
<div class="modal fade" id="userManagersModal" tabindex="-1" aria-labelledby="userManagersModalLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="userManagersModalLabel">
                    <i class="fas fa-users-cog me-2"></i>
                    مديرو المستخدمين ({{ user_managers|length }})
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                    aria-label="Close"></button>
            </div>
            <div class="modal-body">
                {% if user_managers %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead class="table-light">
                            <tr>
                                <th style="color: black !important; background-color: #f8f9fa !important;">اسم المستخدم
                                </th>
                                <th style="color: black !important; background-color: #f8f9fa !important;">البريد
                                    الإلكتروني</th>
                                <th style="color: black !important; background-color: #f8f9fa !important;">الحالة</th>
                                <th style="color: black !important; background-color: #f8f9fa !important;">تاريخ التسجيل
                                </th>
                                <th style="color: black !important; background-color: #f8f9fa !important;">الإجراءات
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for user_manager in user_managers %}
                            <tr>
                                <td>{{ user_manager.username }}</td>
                                <td>{{ user_manager.email }}</td>
                                <td>
                                    {% if user_manager.is_active %}
                                    <span class="badge bg-success">مفعل</span>
                                    {% else %}
                                    <span class="badge bg-danger">معطل</span>
                                    {% endif %}
                                </td>
                                <td>{{ user_manager.created_at.strftime('%Y-%m-%d') if user_manager.created_at else 'غير
                                    محدد' }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        {% if user_manager.is_active %}
                                        <a href="{{ url_for('toggle_user_status', user_id=user_manager.id) }}"
                                            class="btn btn-sm btn-outline-danger"
                                            onclick="return confirm('هل أنت متأكد من تعطيل هذا الحساب؟')">
                                            <i class="fas fa-user-times"></i>
                                        </a>
                                        {% else %}
                                        <a href="{{ url_for('toggle_user_status', user_id=user_manager.id) }}"
                                            class="btn btn-sm btn-outline-success">
                                            <i class="fas fa-user-check"></i>
                                        </a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-users-slash fa-3x text-muted mb-3"></i>
                    <h5>لا يوجد مديرو مستخدمين</h5>
                    <p class="text-muted">لم يتم إنشاء أي حساب مدير مستخدمين بعد</p>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal"
                        data-bs-target="#createUserManagerModal" data-bs-dismiss="modal">
                        <i class="fas fa-user-plus me-1"></i>
                        إنشاء مدير مستخدمين جديد
                    </button>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- مودال إدارة إعدادات الأدوار -->
<div class="modal fade" id="roleSettingsModal" tabindex="-1" aria-labelledby="roleSettingsModalLabel"
    aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="roleSettingsModalLabel">
                    <i class="fas fa-user-cog me-2"></i>
                    إدارة الأدوار المتاحة للتسجيل
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                    aria-label="Close"></button>
            </div>
            <form action="{{ url_for('admin_role_settings') }}" method="post">
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>تنبيه:</strong> يمكنك التحكم في الأدوار التي تظهر للمستخدمين عند التسجيل. الأدوار
                        المعطلة لن تظهر في نموذج التسجيل.
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-body text-center">
                                    <i class="fas fa-chalkboard-teacher fa-3x text-success mb-3"></i>
                                    <h6 class="card-title">دور المعلم</h6>
                                    <div class="form-check form-switch d-flex justify-content-center">
                                        <input class="form-check-input" type="checkbox" id="teacherEnabled"
                                            name="teacher_enabled" checked>
                                        <label class="form-check-label ms-2" for="teacherEnabled">
                                            متاح للتسجيل
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-warning">
                                <div class="card-body text-center">
                                    <i class="fas fa-user-tie fa-3x text-warning mb-3"></i>
                                    <h6 class="card-title">دور المفتش</h6>
                                    <div class="form-check form-switch d-flex justify-content-center">
                                        <input class="form-check-input" type="checkbox" id="inspectorEnabled"
                                            name="inspector_enabled" checked>
                                        <label class="form-check-label ms-2" for="inspectorEnabled">
                                            متاح للتسجيل
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-warning mt-3">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>ملاحظة:</strong> إذا تم تعطيل جميع الأدوار، سيتم عرض الأدوار الافتراضية (معلم ومفتش)
                        تلقائياً.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>
                        إلغاء
                    </button>
                    <button type="submit" class="btn btn-info">
                        <i class="fas fa-save me-1"></i>
                        حفظ الإعدادات
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    // تحميل إعدادات الأدوار الحالية عند فتح المودال
    document.getElementById('roleSettingsModal').addEventListener('show.bs.modal', function () {
        fetch('{{ url_for("admin_role_settings") }}')
            .then(response => response.json())
            .then(data => {
                document.getElementById('teacherEnabled').checked = data.teacher_enabled;
                document.getElementById('inspectorEnabled').checked = data.inspector_enabled;
            })
            .catch(error => {
                console.error('خطأ في تحميل إعدادات الأدوار:', error);
            });
    });
</script>

{% endblock %}

{% block scripts %}
<script>
    // تحديث عدد المستخدمين المتصلين كل 30 ثانية
    function updateOnlineUsersCount() {
        fetch('/api/online-users-count')
            .then(response => response.json())
            .then(data => {
                if (data.count !== undefined) {
                    document.getElementById('online-users-count').textContent = data.count;
                }
            })
            .catch(error => {
                console.log('خطأ في تحديث عدد المستخدمين المتصلين:', error);
            });
    }

    // تحديث العدد كل 30 ثانية
    setInterval(updateOnlineUsersCount, 30000);
</script>
{% endblock %}