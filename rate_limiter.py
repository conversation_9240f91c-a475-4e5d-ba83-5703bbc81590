#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام Rate Limiting متقدم لحماية Ta9affi من إساءة الاستخدام
"""

import time
import json
from datetime import datetime, timedelta
from functools import wraps
from typing import Dict, Optional, <PERSON><PERSON>
from flask import request, jsonify, session, current_app
import redis
from dataclasses import dataclass, asdict
import logging

# إعداد السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class RateLimit:
    """فئة تحديد معدل العمليات"""
    max_requests: int
    window_hours: int
    current_count: int = 0
    window_start: Optional[datetime] = None
    
    def to_dict(self) -> Dict:
        """تحويل إلى قاموس"""
        return {
            'max_requests': self.max_requests,
            'window_hours': self.window_hours,
            'current_count': self.current_count,
            'window_start': self.window_start.isoformat() if self.window_start else None
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'RateLimit':
        """إنشاء من قاموس"""
        window_start = None
        if data.get('window_start'):
            window_start = datetime.fromisoformat(data['window_start'])
        
        return cls(
            max_requests=data['max_requests'],
            window_hours=data['window_hours'],
            current_count=data.get('current_count', 0),
            window_start=window_start
        )

class AdvancedRateLimiter:
    """نظام Rate Limiting متقدم"""

    def __init__(self, redis_client=None):
        self.redis_client = redis_client

        # تخزين محلي للبيانات عند عدم وجود Redis
        self.local_storage = {}

        # قواعد Rate Limiting للعمليات المختلفة
        self.rate_limits = {
            'add_progress': RateLimit(max_requests=10, window_hours=12),
            'delete_progress': RateLimit(max_requests=3, window_hours=12),
            'login_attempts': RateLimit(max_requests=5, window_hours=1),
            'password_reset': RateLimit(max_requests=3, window_hours=24),
            'file_upload': RateLimit(max_requests=20, window_hours=1),
            'api_calls': RateLimit(max_requests=100, window_hours=1),
        }
    
    def _get_user_key(self, user_id: int, action: str) -> str:
        """إنشاء مفتاح فريد للمستخدم والعملية"""
        return f"rate_limit:{user_id}:{action}"
    
    def _get_ip_key(self, ip: str, action: str) -> str:
        """إنشاء مفتاح فريد للـ IP والعملية"""
        return f"rate_limit:ip:{ip}:{action}"
    
    def _get_current_window_start(self, window_hours: int) -> datetime:
        """حساب بداية النافذة الزمنية الحالية"""
        now = datetime.now()
        hours_since_midnight = now.hour
        window_start_hour = (hours_since_midnight // window_hours) * window_hours
        return now.replace(hour=window_start_hour, minute=0, second=0, microsecond=0)
    
    def _get_rate_limit_data(self, key: str, action: str) -> RateLimit:
        """استرجاع بيانات Rate Limit"""
        if self.redis_client:
            try:
                data = self.redis_client.get(key)
                if data:
                    rate_limit_data = json.loads(data)
                    return RateLimit.from_dict(rate_limit_data)
            except Exception as e:
                logger.error(f"خطأ في استرجاع بيانات Rate Limit من Redis: {e}")

        # استخدام التخزين المحلي إذا لم يكن Redis متاح
        if key in self.local_storage:
            try:
                return RateLimit.from_dict(self.local_storage[key])
            except Exception as e:
                logger.error(f"خطأ في استرجاع بيانات Rate Limit من التخزين المحلي: {e}")

        # إرجاع قيم افتراضية
        default_limit = self.rate_limits.get(action, RateLimit(max_requests=10, window_hours=12))
        return RateLimit(
            max_requests=default_limit.max_requests,
            window_hours=default_limit.window_hours,
            current_count=0,
            window_start=None
        )
    
    def _save_rate_limit_data(self, key: str, rate_limit: RateLimit):
        """حفظ بيانات Rate Limit"""
        if self.redis_client:
            try:
                # حفظ لمدة 48 ساعة (ضعف أطول نافزة زمنية)
                expire_seconds = 48 * 3600
                self.redis_client.setex(
                    key,
                    expire_seconds,
                    json.dumps(rate_limit.to_dict())
                )
                return
            except Exception as e:
                logger.error(f"خطأ في حفظ بيانات Rate Limit في Redis: {e}")

        # حفظ في التخزين المحلي إذا لم يكن Redis متاح
        try:
            self.local_storage[key] = rate_limit.to_dict()
        except Exception as e:
            logger.error(f"خطأ في حفظ بيانات Rate Limit في التخزين المحلي: {e}")
    
    def check_rate_limit(self, user_id: int, action: str, ip: str = None) -> Tuple[bool, Dict]:
        """
        فحص Rate Limit للمستخدم
        
        Args:
            user_id: معرف المستخدم
            action: نوع العملية
            ip: عنوان IP (اختياري)
        
        Returns:
            (allowed, info) - هل العملية مسموحة ومعلومات إضافية
        """
        try:
            # فحص Rate Limit للمستخدم مع التحقق من الإعدادات المخصصة
            user_key = self._get_user_key(user_id, action)

            # محاولة الحصول على الإعدادات المخصصة من قاعدة البيانات
            try:
                from rate_limit_settings import get_user_rate_limits
                user_limits = get_user_rate_limits(user_id)

                if action in user_limits:
                    # استخدام الحدود المخصصة
                    custom_limit = RateLimit(
                        max_requests=user_limits[action]['max_requests'],
                        window_hours=user_limits[action]['window_hours'],
                        current_count=0,
                        window_start=None
                    )
                    # الحصول على البيانات الحالية وتحديث الحدود
                    current_data = self._get_rate_limit_data(user_key, action)
                    custom_limit.current_count = current_data.current_count
                    custom_limit.window_start = current_data.window_start
                    rate_limit = custom_limit
                else:
                    rate_limit = self._get_rate_limit_data(user_key, action)
            except Exception as e:
                # في حالة فشل الوصول لقاعدة البيانات، استخدم الإعدادات الافتراضية
                logger.warning(f"فشل في الحصول على إعدادات المستخدم {user_id}: {e}")
                rate_limit = self._get_rate_limit_data(user_key, action)
            
            now = datetime.now()
            window_start = self._get_current_window_start(rate_limit.window_hours)
            
            # إذا كانت نافذة جديدة، إعادة تعيين العداد
            if not rate_limit.window_start or rate_limit.window_start < window_start:
                rate_limit.window_start = window_start
                rate_limit.current_count = 0
            
            # فحص الحد الأقصى
            if rate_limit.current_count >= rate_limit.max_requests:
                # حساب وقت إعادة التعيين
                next_reset = window_start + timedelta(hours=rate_limit.window_hours)
                time_until_reset = next_reset - now
                
                return False, {
                    'error': 'rate_limit_exceeded',
                    'message': f'تم تجاوز الحد الأقصى للعملية {action}',
                    'max_requests': rate_limit.max_requests,
                    'window_hours': rate_limit.window_hours,
                    'current_count': rate_limit.current_count,
                    'time_until_reset': str(time_until_reset),
                    'reset_time': next_reset.isoformat()
                }
            
            # العملية مسموحة
            return True, {
                'allowed': True,
                'remaining_requests': rate_limit.max_requests - rate_limit.current_count,
                'window_hours': rate_limit.window_hours,
                'current_count': rate_limit.current_count,
                'reset_time': (window_start + timedelta(hours=rate_limit.window_hours)).isoformat()
            }
            
        except Exception as e:
            logger.error(f"خطأ في فحص Rate Limit: {e}")
            # في حالة الخطأ، السماح بالعملية
            return True, {'error': 'rate_limit_check_failed'}
    
    def increment_counter(self, user_id: int, action: str, ip: str = None):
        """زيادة عداد العمليات"""
        try:
            user_key = self._get_user_key(user_id, action)
            rate_limit = self._get_rate_limit_data(user_key, action)
            
            now = datetime.now()
            window_start = self._get_current_window_start(rate_limit.window_hours)
            
            # إذا كانت نافذة جديدة، إعادة تعيين العداد
            if not rate_limit.window_start or rate_limit.window_start < window_start:
                rate_limit.window_start = window_start
                rate_limit.current_count = 0
            
            # زيادة العداد
            rate_limit.current_count += 1
            
            # حفظ البيانات
            self._save_rate_limit_data(user_key, rate_limit)
            
            logger.info(f"تم تسجيل عملية {action} للمستخدم {user_id}. العدد الحالي: {rate_limit.current_count}")
            
        except Exception as e:
            logger.error(f"خطأ في زيادة عداد Rate Limit: {e}")
    
    def get_user_limits_status(self, user_id: int) -> Dict:
        """الحصول على حالة جميع حدود المستخدم"""
        status = {}
        
        for action in ['add_progress', 'delete_progress']:
            user_key = self._get_user_key(user_id, action)
            rate_limit = self._get_rate_limit_data(user_key, action)
            
            now = datetime.now()
            window_start = self._get_current_window_start(rate_limit.window_hours)
            
            # إذا كانت نافذة جديدة، إعادة تعيين العداد
            if not rate_limit.window_start or rate_limit.window_start < window_start:
                rate_limit.window_start = window_start
                rate_limit.current_count = 0
            
            next_reset = window_start + timedelta(hours=rate_limit.window_hours)
            
            status[action] = {
                'max_requests': rate_limit.max_requests,
                'current_count': rate_limit.current_count,
                'remaining': rate_limit.max_requests - rate_limit.current_count,
                'window_hours': rate_limit.window_hours,
                'reset_time': next_reset.isoformat(),
                'time_until_reset': str(next_reset - now)
            }
        
        return status
    
    def reset_user_limits(self, user_id: int, action: str = None):
        """إعادة تعيين حدود المستخدم (للإدارة)"""
        try:
            if action:
                actions = [action]
            else:
                actions = ['add_progress', 'delete_progress', 'login_attempts', 'password_reset']
            
            for act in actions:
                user_key = self._get_user_key(user_id, act)
                if self.redis_client:
                    self.redis_client.delete(user_key)
            
            logger.info(f"تم إعادة تعيين حدود المستخدم {user_id} للعمليات: {actions}")
            
        except Exception as e:
            logger.error(f"خطأ في إعادة تعيين حدود المستخدم: {e}")

# إنشاء مثيل عام
rate_limiter = AdvancedRateLimiter()

def init_rate_limiter(redis_client):
    """تهيئة Rate Limiter مع Redis"""
    global rate_limiter
    rate_limiter = AdvancedRateLimiter(redis_client)

def rate_limit(action: str, use_ip: bool = False):
    """
    Decorator لتطبيق Rate Limiting على الدوال
    
    Args:
        action: نوع العملية
        use_ip: استخدام IP بدلاً من معرف المستخدم
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                # الحصول على معرف المستخدم
                user_id = None
                if hasattr(session, 'get') and session.get('user_id'):
                    user_id = session.get('user_id')
                elif hasattr(request, 'form') and request.form.get('user_id'):
                    user_id = int(request.form.get('user_id'))
                
                # الحصول على IP
                ip = request.remote_addr if hasattr(request, 'remote_addr') else None
                
                # إذا لم يكن هناك معرف مستخدم واستخدام IP مطلوب
                if not user_id and use_ip and ip:
                    user_id = f"ip_{ip}"
                
                if not user_id:
                    # إذا لم نتمكن من تحديد المستخدم، السماح بالعملية
                    return f(*args, **kwargs)
                
                # فحص Rate Limit
                allowed, info = rate_limiter.check_rate_limit(user_id, action, ip)
                
                if not allowed:
                    # إرجاع خطأ Rate Limit
                    if hasattr(request, 'is_json') and request.is_json:
                        return jsonify({
                            'success': False,
                            'error': 'rate_limit_exceeded',
                            'message': info.get('message', 'تم تجاوز الحد الأقصى للطلبات'),
                            'details': info
                        }), 429
                    else:
                        # للطلبات العادية، إرجاع صفحة خطأ أو رسالة
                        from flask import flash, redirect, url_for
                        flash(info.get('message', 'تم تجاوز الحد الأقصى للطلبات. يرجى المحاولة لاحقاً.'), 'error')
                        return redirect(url_for('dashboard'))
                
                # تنفيذ الدالة
                result = f(*args, **kwargs)
                
                # زيادة العداد بعد نجاح العملية
                rate_limiter.increment_counter(user_id, action, ip)
                
                return result
                
            except Exception as e:
                logger.error(f"خطأ في Rate Limiting: {e}")
                # في حالة الخطأ، السماح بالعملية
                return f(*args, **kwargs)
        
        return decorated_function
    return decorator

def check_user_rate_limits(user_id: int) -> Dict:
    """فحص حالة Rate Limits للمستخدم"""
    return rate_limiter.get_user_limits_status(user_id)

def reset_user_rate_limits(user_id: int, action: str = None):
    """إعادة تعيين Rate Limits للمستخدم (للإدارة)"""
    rate_limiter.reset_user_limits(user_id, action)

# دوال مساعدة للاستخدام المباشر
def can_add_progress(user_id: int) -> Tuple[bool, Dict]:
    """فحص إمكانية إضافة تقدم"""
    return rate_limiter.check_rate_limit(user_id, 'add_progress')

def can_delete_progress(user_id: int) -> Tuple[bool, Dict]:
    """فحص إمكانية حذف تقدم"""
    return rate_limiter.check_rate_limit(user_id, 'delete_progress')

def record_add_progress(user_id: int):
    """تسجيل عملية إضافة تقدم"""
    rate_limiter.increment_counter(user_id, 'add_progress')

def record_delete_progress(user_id: int):
    """تسجيل عملية حذف تقدم"""
    rate_limiter.increment_counter(user_id, 'delete_progress')
